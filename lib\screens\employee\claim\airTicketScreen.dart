import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/claim/components/claimtype.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customSubmitConfirmDialog.dart';
import 'package:seawork/screens/employee/claim/components/airTicketAllownace.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';

class AirTicketScreen extends StatefulWidget {
  final bool hideAppBar;
  final bool showImage;
  final bool showClaimTypeDropdown;
  final String? initialClaimType; // Added this field

  const AirTicketScreen({
    Key? key,
    this.hideAppBar = false,
    this.showImage = false,
    this.showClaimTypeDropdown = false,
    this.initialClaimType, // Added this parameter
  }) : super(key: key);

  @override
  _AirTicketScreenState createState() => _AirTicketScreenState();
}

class _AirTicketScreenState extends State<AirTicketScreen> {
  String? selectedSelfOption;
  List<String> selectedSpouses = [];
  List<String> selectedDependents = [];
  List<FileModel> uploadedFiles = [];
  List<TextEditingController> _linkControllers = [];
  String? reimbursementTypes;
  String? currentClaimType; // Added this to track current claim type

  final List<String> spouseOptions = ['Fathima', 'Nadhiya Ansar'];
  final List<String> dependentOptions = ['Abdhul', 'Akhbar'];

  @override
  void initState() {
    super.initState();
    currentClaimType =
        widget.initialClaimType ??
        "Air ticket allowance"; // Initialize claim type
    _addNewLinkField();
  }

  void _addNewLinkField() {
    final controller = TextEditingController();
    controller.addListener(() {
      if (controller.text.isNotEmpty && _linkControllers.last == controller) {
        _addNewLinkField();
      } else if (controller.text.isEmpty) {
        _removeNewestLinkField();
      }
    });
    setState(() {
      _linkControllers.add(controller);
    });
  }

  void _removeNewestLinkField() {
    if (_linkControllers.length > 1) {
      setState(() {
        _linkControllers.last.dispose();
        _linkControllers.removeLast();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool shouldShowImage = widget.hideAppBar ? widget.showImage : false;

    return Consumer(
      builder: (context, ref, child) {
return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:
        Scaffold(
          backgroundColor: AppColors.secondaryColor,
          appBar: CustomAppBar(
            title:
                widget.showClaimTypeDropdown
                    ? 'Add claims'
                    : 'Air ticket allowance',
            showActionIcon: true,
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  if (widget.showClaimTypeDropdown) ...[
                    Padding(
                      padding: const EdgeInsets.only(),
                      child: HeadingText(text: "Claim type", hasAsterisk: true),
                    ),
                    const SizedBox(height: 8),
                    ClaimTypeSelector(
                      initialClaimType:
                          currentClaimType, // Pass the initial claim type
                      onClaimTypeChanged: (newClaimType) {
                        setState(() {
                          currentClaimType = newClaimType;
                        });
                      },
                    ),
                    SizedBox(height: 24),
                  ],
                  AirTicketForm(
                    dependentOptions: dependentOptions,
                    onAddLinkField: _addNewLinkField,
                    onDependentsChanged: (value) {
                      setState(() {
                        selectedDependents = value;
                      });
                    },
                    onRemoveLinkField: _removeNewestLinkField,
                    onSpousesChanged: (value) {
                      setState(() {
                        selectedSpouses = value;
                      });
                    },
                    onFilesChanged: (files) {
                      setState(() {
                        uploadedFiles = files;
                      });
                    },
                    spouseOptions: spouseOptions,
                    selectedSelfOption: selectedSelfOption,
                    selectedSpouses: selectedSpouses,
                    selectedDependents: selectedDependents,
                    uploadedFiles: uploadedFiles,
                    linkControllers: _linkControllers,
                    onSelfOptionChanged: (value) {
                      setState(() {
                        selectedSelfOption = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed:
                  selectedSelfOption != null
                      ? () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => CustomSubmitConfirmDialog(
                                content1: 'Claim',
                                type: 'Claim',
                                content3: "Air ticket allowance",
                                message:
                                    'Your claim has been submitted and \n ready to be reviewed',
                                onClose: () {
                                  Navigator.of(context).pop();

                                  Future.microtask(() {
                                    if (widget.showClaimTypeDropdown) {
                                      context.pushReplacement('/claim');
                                    } else {
                                      context.pushReplacement('/dashboard');
                                    }
                                  });
                                },

                                documentType: 'Air ticket allowance',
                                document: CustomSubmitConfirmDialog,
                              ),
                        );
                      }
                      : null,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    selectedSelfOption != null
                        ? AppColors.viewColor
                        : AppColors.lightGreyColor2,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: DmSansText(
                'Submit request',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color:
                    selectedSelfOption != null
                        ? AppColors.whiteColor
                        : AppColors.lightGreyshade,
              ),
            ),
          ),
));
      },
    );
  }
}

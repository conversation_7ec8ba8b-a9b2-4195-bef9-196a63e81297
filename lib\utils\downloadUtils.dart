import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:mime/mime.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:universal_html/html.dart' as web;

Future<void> downloadFileWithDio({
  required Dio dio,
  required String url,
  required String fileName,
  required dynamic context, // Pass BuildContext if you want to show SnackBar
}) async {
  try {
    final sessionToken = await PreferencesUtils.getString(
      PreferencesUtils.SESSION_TOKEN,
    );

    // Fetch the file as bytes
    final response = await dio.get<List<int>>(
      url,
      options: Options(
        responseType: ResponseType.bytes,
        headers: {
          'Authorization': 'Bearer $sessionToken',
          'Accept': 'application/pdf',
        },
      ),
    );

    final bytes = response.data;
    if (bytes == null) {
      throw Exception('No data received from server');
    }

    if (kIsWeb) {
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: Uint8List.fromList(bytes),
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );
    } else {
      await FileSaver.instance.saveAs(
        name: fileName,
        bytes: Uint8List.fromList(bytes),
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('File downloaded successfully: $fileName')),
        );
      }
    }
  } on DioException catch (dioError) {
    String errorMsg =
        'Download failed: ${dioError.error?.toString() ?? dioError.message}';
    if (dioError.response != null) {
      errorMsg +=
          '\nStatus code: ${dioError.response?.statusCode}\n'
          'Response: ${dioError.response?.data}';
    }
    if (context != null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(errorMsg)));
    }
    rethrow;
  } catch (e) {
    if (context != null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Download failed: $e')));
    }
  }
}

class DownloadUtilsForBytes {
  static Future<void> saveAsBytes(Uint8List bytes,
  {required String fileName, String ? mimeType}
  )async{
    final mime = mimeType ?? lookupMimeType('', headerBytes: bytes);

    if(kIsWeb){
      final blob = web.Blob([bytes], mime);
      final url = web.Url.createObjectUrl(blob);
      final anchor = web.AnchorElement(href: url)..download = fileName..style.display = "none";
      web.document.body?.append(anchor);
      anchor.click();
      web.Url.revokeObjectUrl(url);
      anchor.remove();
      return;
    }

    await FileSaver.instance.saveAs(
      name: fileName,
      bytes: bytes,
      ext: _getFileExtensionFromMimeType(mime) ?? 'bin',
      mimeType: _getMimeTypeEnum(mime),
    );
 }

  static String? _getFileExtensionFromMimeType(String? mimeType) {
    if (mimeType == null) return null;
    
    switch (mimeType) {
      case 'application/pdf':
        return 'pdf';
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      case 'image/gif':
        return 'gif';
      case 'text/plain':
        return 'txt';
      case 'application/json':
        return 'json';
      case 'application/xml':
      case 'text/xml':
        return 'xml';
      case 'application/zip':
        return 'zip';
      case 'application/msword':
        return 'doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'docx';
      case 'application/vnd.ms-excel':
        return 'xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return 'xlsx';
      default:
        final parts = mimeType.split('/');
        if (parts.length == 2) {
          return parts[1].split('+').first; 
        }
        return null;
    }
  }
  static MimeType _getMimeTypeEnum(String? mimeType) {
    if (mimeType == null) return MimeType.other;
    
    switch (mimeType) {
      case 'application/pdf':
        return MimeType.pdf;
      case 'image/jpeg':
        return MimeType.jpeg;
      case 'image/png':
        return MimeType.png;
      case 'image/gif':
        return MimeType.gif;
      case 'text/plain':
        return MimeType.text;
      case 'application/json':
        return MimeType.json;
      case 'application/xml':
      case 'text/xml':
        return MimeType.other; 
      case 'application/zip':
        return MimeType.zip;
      case 'application/msword':
        return MimeType.microsoftWord;
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return MimeType.microsoftWord; 
      case 'application/vnd.ms-excel':
        return MimeType.microsoftExcel;
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return MimeType.microsoftExcel; 
      case 'text/csv':
        return MimeType.csv;
      case 'image/bmp':
        return MimeType.bmp;
      case 'application/epub+zip':
        return MimeType.epub;
      case 'video/mp4':
        return MimeType.mp4Video;
      case 'audio/mp4':
        return MimeType.mp4Audio;
      case 'audio/mpeg':
        return MimeType.mpeg;
      case 'audio/mp3':
        return MimeType.mp3;
      case 'audio/aac':
        return MimeType.aac;
      case 'video/x-msvideo':
        return MimeType.avi;
      case 'application/vnd.rar':
        return MimeType.rar;
      default:
        return MimeType.other;
    }
  }
}

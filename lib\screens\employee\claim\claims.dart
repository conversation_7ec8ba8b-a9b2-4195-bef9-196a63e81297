import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customMainTab.dart';
import 'package:seawork/components/widget/customTabbar.dart';
import 'package:seawork/screens/employee/absence/providers/tabProvider.dart';
import 'package:seawork/screens/employee/claim/components/claimTypesScreen.dart';
import 'package:seawork/screens/employee/claim/components/customClaimTabbar.dart';
import 'package:seawork/utils/style/colors.dart';

class ClaimScreen extends ConsumerStatefulWidget {
  const ClaimScreen({super.key});

  @override
  ConsumerState<ClaimScreen> createState() => _ClaimScreenState();
}

class _ClaimScreenState extends ConsumerState<ClaimScreen>
    with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  TabController? _createdByMeController;
  TabController? _assignedToMeController;
  Key _createdByMeKey = UniqueKey();
  Key _assignedToMeKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    _createdByMeController = TabController(
      length: 4,
      vsync: this,
      initialIndex: 0,
    );
    _assignedToMeController = TabController(
      length: 4,
      vsync: this,
      initialIndex: 0,
    );
  }

  @override
  void dispose() {
    _createdByMeController?.dispose();
    _assignedToMeController?.dispose();
    super.dispose();
  }

  void _changeStatusTab(bool isRightSwipe) {
    TabController? currentController =
        _selectedTabIndex == 0 ? _createdByMeController : _assignedToMeController;

    if (currentController != null) {
      int currentIndex = currentController.index;
      int maxIndex = currentController.length - 1;

      if (isRightSwipe) {
        if (currentIndex > 0) {
          currentController.animateTo(currentIndex - 1);
        } else {
          currentController.animateTo(maxIndex);
        }
      } else {
        if (currentIndex < maxIndex) {
          currentController.animateTo(currentIndex + 1);
        } else {
          currentController.animateTo(0);
        }
      }

      String statusName = _getStatusName(currentController.index);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Switched to $statusName'),
          duration: const Duration(milliseconds: 1000),
          backgroundColor: _getStatusColor(currentController.index),
        ),
      );
    }
  }

  String _getStatusName(int index) {
    switch (index) {
      case 0:
        return 'Pending';
      case 1:
        return 'Approved';
      case 2:
        return 'Rejected';
      case 3:
        return 'Withdrawn';
      default:
        return 'Pending';
    }
  }

  Color _getStatusColor(int index) {
    switch (index) {
      case 0:
        return AppColors.goldenYellow;
      case 1:
        return AppColors.seaGreen;
      case 2:
        return AppColors.Orange;
      case 3:
        return AppColors.softPurple;
      default:
        return AppColors.goldenYellow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (!didPop) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          onBackPressed: () => Navigator.pop(context),
          title: 'Claims',
          showActionIcon: true,
        ),
        body: GestureDetector(
          onPanEnd: (details) {
            if (details.velocity.pixelsPerSecond.dx.abs() >
                    details.velocity.pixelsPerSecond.dy.abs() &&
                details.velocity.pixelsPerSecond.dx.abs() > 300) {
              bool isRightSwipe = details.velocity.pixelsPerSecond.dx > 0;
              _changeStatusTab(isRightSwipe);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18.0),
                  child: CustomMainTabBar(
                    selectedIndex: _selectedTabIndex,
                    onTabSelected: (index) {
                      setState(() {
                        _selectedTabIndex = index;
                        ref.read(selectedTabProvider.notifier).state =
                            _selectedTabIndex;
                      });
                    },
                    tabs: const [
                      TabItem(label: 'Created by me', isDynamic: true),
                      TabItem(label: 'Assigned to me', badgeCount: 5),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                if (_selectedTabIndex == 0)
                  Expanded(
                    child: DefaultTabController(
                      length: 4,
                      child: Builder(
                        builder: (context) {
                          TabController tabController =
                              DefaultTabController.of(context);
                          return CustomTabBar(
                            iconClicked: 'Claim',
                            length: 4,
                            onTabChanged: (String status) {
                              print('Tab changed to: $status');
                            },
                            buildTabContent: (
                              String status, {
                              int? selectedTabIndex,
                              String? iconClicked,
                            }) {
                              switch (status) {
                                case 'pending':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6.0,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'pending',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: selectedTabIndex ?? 0,
                                    ),
                                  );
                                case 'approved':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'approved',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: selectedTabIndex ?? 0,
                                    ),
                                  );
                                case 'rejected':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'rejected',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: selectedTabIndex ?? 0,
                                    ),
                                  );
                                case 'withdrawn':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'withdrawn',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: selectedTabIndex ?? 0,
                                    ),
                                  );
                                default:
                                  return Container(
                                    child: Center(
                                      child: Text('No content for $status'),
                                    ),
                                  );
                              }
                            },
                          );
                        },
                      ),
                    ),
                  )
                else if (_selectedTabIndex == 1)
                  Expanded(
                    child: DefaultTabController(
                      length: 4,
                      child: Builder(
                        builder: (context) {
                          TabController tabController =
                              DefaultTabController.of(context);
                          return CustomTabBar(
                            iconClicked: 'Claim',
                            length: 4,
                            onTabChanged: (String status) {
                              print('Tab changed to: $status');
                            },
                            buildTabContent: (
                              String status, {
                              int? selectedTabIndex,
                              String? iconClicked,
                            }) {
                              switch (status) {
                                case 'pending':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'pending',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: 1,
                                    ),
                                  );
                                case 'approved':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'approved',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: 1,
                                    ),
                                  );
                                case 'rejected':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'rejected',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: 1,
                                    ),
                                  );
                                case 'withdrawn':
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                    ),
                                    child: ClaimTabBarWidget(
                                      status: 'withdrawn',
                                      iconClicked: 'Claim',
                                      selectedTabIndex: 1,
                                    ),
                                  );
                                default:
                                  return Container(
                                    child: Center(
                                      child: Text('No content for $status'),
                                    ),
                                  );
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(bottom: 52.0),
          child: FloatingActionButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                isDismissible: true,
                enableDrag: true,
                builder: (context) {
                  return DraggableScrollableSheet(
                    initialChildSize: 0.6,
                    minChildSize: 0.3,
                    maxChildSize: 0.85,
                    expand: false,
                    builder: (context, scrollController) {
                      return ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20.0),
                          topRight: Radius.circular(20.0),
                        ),
                        child: ClaimTypesScreen(
                          scrollController: scrollController,
                        ),
                      );
                    },
                  );
                },
              );
            },
            shape: const CircleBorder(),
            child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
          ),
        ),
        bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
      ),
    );
  }
}
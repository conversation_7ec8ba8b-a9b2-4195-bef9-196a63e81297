import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/claim/selectedTravelClaim.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';

// Define a provider for the selected travel claim type
final travelClaimProvider = StateProvider<String?>((ref) => null);

class TravelClaim extends ConsumerStatefulWidget {
  const TravelClaim({super.key});

  @override
  ConsumerState<TravelClaim> createState() => _TravelClaimState();
}

class _TravelClaimState extends ConsumerState<TravelClaim> {
  bool isBottomSheetOpen = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:
    Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: 'Travel claims',
        showActionIcon: true,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20.0, top: 32.0, bottom: 4.0),
            child: Row(
              children: [
                HeadingText(text: "Travel claim type", hasAsterisk: true),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: GestureDetector(
              onTap: () async {
                setState(() => isBottomSheetOpen = true);

                await showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  isDismissible: true,
                  enableDrag: true,
                  barrierColor: AppColors.blackColor.withOpacity(0.5),
                  builder: (context) {
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.70,
                        child: DraggableScrollableSheet(
                          initialChildSize: 0.4,
                          minChildSize: 0.4,
                          maxChildSize: 1.0,
                          builder:
                              (context, scrollController) => ClipRRect(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(20.0),
                                  topRight: Radius.circular(20.0),
                                ),
                                child: SelectTravelClaimScreen(),
                              ),
                        ),
                      ),
                    );
                  },
                );
                setState(() => isBottomSheetOpen = false);
              },
              child: Container(
                width: double.infinity,
                height: 43,
                decoration: BoxDecoration(
                  color: AppColors.whiteColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.lightGreyColor2,
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OpenSansText(
                        'Select type',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.lightGreyshade,
                      ),
                      CustomSvgImage(
                        imageName: isBottomSheetOpen ? "Arrowup" : "ArrowLeft",
                        height: 24,
                        width: 24,
                        color: AppColors.viewColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
     ) );
  }
}

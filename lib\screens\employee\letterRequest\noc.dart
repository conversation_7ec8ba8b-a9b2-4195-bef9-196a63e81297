import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/letterRequest/components/request_letter_bottom_sheet.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/countryDropDown.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:go_router/go_router.dart';

final documentTypeProvider = StateProvider<String?>((ref) => null);
final countryProvider = StateProvider<String?>((ref) => null);
final startDateProvider = StateProvider<DateTime?>((ref) => null);
final endDateProvider = StateProvider<DateTime?>((ref) => null);

class Noobjection extends ConsumerStatefulWidget {
  const Noobjection({super.key});

  @override
  ConsumerState<Noobjection> createState() => NoobjectionState();
}

class NoobjectionState extends ConsumerState<Noobjection> {
  List<FileModel> uploadedFiles = [];
  DateTime? startDate;
  DateTime? endDate;
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  bool _isBottomSheetOpen = false;

  List<FileModel> selectedFiles = [];

  bool isFormValid() {
    final selectedType = ref.watch(documentTypeProvider);
    final selectedCountry = ref.watch(countryProvider);
    final startDate = ref.watch(startDateProvider);
    final endDate = ref.watch(endDateProvider);

    if (selectedType == 'Embassy letter') {
      return selectedType != null &&
          selectedCountry != null &&
          startDate != null &&
          endDate != null;
    }
    return true; // For other document types
  }

  bool isAsteriskFieldsFilled() {
    final selectedType = ref.watch(documentTypeProvider);
    final selectedCountry = ref.watch(countryProvider);
    final startDate = ref.watch(startDateProvider);
    final endDate = ref.watch(endDateProvider);

    if (selectedType == 'Embassy letter') {
      return selectedType != null &&
          selectedCountry != null &&
          startDate != null &&
          endDate != null;
    } else if (selectedType == 'Noc for driver license') {
      // For "Noc for driver license", only the document type selection is required
      return selectedType != null;
    }

    return false; // If no document type is selected
  }

  bool isLinkOrAttachmentFilled() {
    final selectedType = ref.watch(documentTypeProvider);
    return (selectedType == 'Noc for driver license' ||
            selectedType == 'Embassy letter') &&
        (uploadedFiles.isNotEmpty || /* Check if link input is filled */ false);
  }

  @override
  Widget build(BuildContext context) {
    final selectedType = ref.watch(documentTypeProvider);
    final selectedCountry = ref.watch(countryProvider);

   return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          color: AppColors.secondaryColor,
          child: CustomAppBar(title: 'No objection certificate'),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 32.0,
                    left: 20.0,
                    right: 20.0,
                    bottom: 20,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HeadingText(text: 'Document type', hasAsterisk: true),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () async {
                          setState(() {
                            _isBottomSheetOpen = true;
                          });

                          await showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            barrierColor: Colors.black.withOpacity(0.5),
                            isDismissible: true,
                            enableDrag: true,
                            builder: (context) {
                              return Stack(
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Container(color: Colors.transparent),
                                  ),
                                  DraggableScrollableSheet(
                                    initialChildSize: 0.2,
                                    minChildSize: 0.2,
                                    maxChildSize: 1.0,
                                    builder: (context, scrollController) {
                                      return ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(20.0),
                                          topRight: Radius.circular(20.0),
                                        ),
                                        child: RequestLetterBottomSheet('noc'),
                                      );
                                    },
                                  ),
                                ],
                              );
                            },
                          );
                          setState(() {
                            _isBottomSheetOpen = false;
                            ref.read(selectedOptionProvider.notifier).state =
                                null;
                          });
                        },
                        child: Container(
                          height: 43,
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.lightGreyColor2,
                              width: 1,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                OpenSansText(
                                  selectedType ?? 'Select type',
                                  fontSize: 14,
                                  color: AppColors.blackColor,
                                ),
                                CustomSvgImage(
                                  imageName:
                                      _isBottomSheetOpen
                                          ? "Arrowup"
                                          : "ArrowLeft",
                                  color: AppColors.viewColor,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (selectedType == 'Embassy letter') ...[
                        const SizedBox(height: 24),
                        HeadingText(
                          text: 'Travelling country',
                          hasAsterisk: true,
                        ),
                        SizedBox(height: 8),
                        Container(
                          child: CountryDropdownOverlay(
                            countryProvider: countryProvider,
                          ),
                        ),
                        CustomDatePickerField(
                          dateText: 'Travel start date',
                          hintText: 'dd/mm/yyyy',
                          controller: _startDateController,
                          onDateSelected: (date) {
                            ref.read(startDateProvider.notifier).state =
                                date; // Update state
                          },
                          isStartDate: true,
                        ),
                        CustomDatePickerField(
                          dateText: 'Travel end date',
                          hintText: 'dd/mm/yyyy',
                          controller: _endDateController,
                          onDateSelected: (date) {
                            ref.read(endDateProvider.notifier).state =
                                date; // Update state
                          },
                          isStartDate: false,
                        ),
                      ],
                      if (selectedType == 'Noc for driver license' ||
                          selectedType == 'Embassy letter') ...[
                        const SizedBox(height: 24),
                        HeadingText(text: "Attachment"),
                        // const SizedBox(height: 8),
                        AttachmentField(
                          onFilesChanged: (p0) {},
                          // onFilesUpdated: _updateUploadedFiles,
                          uploadedFiles: uploadedFiles,
                        ),
                      ],
                      if (selectedType == 'Noc for driver license' ||
                          selectedType == 'Embassy letter') ...[
                        const SizedBox(height: 24),
                        HeadingText(text: "Link"),
                        const SizedBox(height: 8),
                        LinkInputList(),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            if (selectedType == 'Noc for driver license' ||
                selectedType == 'Embassy letter')
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 32,
                ),
                child: Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: 52,
                      child: OutlinedButton(
                        onPressed: () {
                          // Add view draft functionality
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppColors.viewColor),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: DmSansText(
                          'View draft',
                          color: AppColors.viewColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      height: 52,
                      child: ElevatedButton(
                        onPressed:
                            isAsteriskFieldsFilled()
                                ? () {
                                  // Add submit request functionality
                                }
                                : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isAsteriskFieldsFilled()
                                  ? AppColors.viewColor
                                  : AppColors.lightGreyshade,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: DmSansText(
                          'Submit request',
                          color: AppColors.whiteColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    ) );
  }
}

class DocumentTypeBottomSheet extends ConsumerWidget {
  const DocumentTypeBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<String> documentTypes = [
      'Noc for driver license',
      'Embassy letter',
    ];

    final selectedType = ref.watch(documentTypeProvider);

    return Container(
      height: MediaQuery.of(context).size.height * 0.19,
      decoration: const BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Container(
              width: 30,
              height: 5,
              decoration: BoxDecoration(
                color: AppColors.viewColor,
                borderRadius: BorderRadius.circular(9),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: documentTypes.length,
              itemBuilder: (context, index) {
                final isSelected = documentTypes[index] == selectedType;
                return Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppColors.lightGreyColor2
                              : AppColors.transparentColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: ListTile(
                      title: OpenSansText(
                        documentTypes[index],
                        textAlign: TextAlign.center,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                        color: AppColors.blackColor,
                      ),
                      onTap: () {
                        ref.read(documentTypeProvider.notifier).state =
                            documentTypes[index];
                        context.push('/noc');
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

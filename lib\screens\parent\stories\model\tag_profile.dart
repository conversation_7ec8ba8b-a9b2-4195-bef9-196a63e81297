// Taggable users returned by GetAllTagProfilesByUserId
class TagProfile {
  final int id;
  final int userType;              // 1-Parent | 2-Staff | 3-Student
  final String nameEn;
  final String nameAr;
  final String? email;             // null for students
  final String? role;              // staff only
  final String? profileKey;        // may be empty
  final String? childCode;         // students only
  final String? nurseryEn;
  final String? nurseryAr;
  final String? gradeEn;
  final String? gradeAr;
  final String? sectionEn;
  final String? sectionAr;

  TagProfile({
    required this.id,
    required this.userType,
    required this.nameEn,
    required this.nameAr,
    this.email,
    this.role,
    this.profileKey,
    this.childCode,
    this.nurseryEn,
    this.nurseryAr,
    this.gradeEn,
    this.gradeAr,
    this.sectionEn,
    this.sectionAr,
  });

  factory TagProfile.fromJson(Map<String, dynamic> j) => TagProfile(
        id: j['Id'],
        userType: j['UserType'],
        nameEn: j['NameInEnglish'] ?? '',
        nameAr: j['NameInArabic'] ?? '',
        email: j['Email'],
        role: j['UserRole'],
        profileKey: j['ProfileDocumentUpload'],
        childCode: j['ChildCode'],
        nurseryEn: j['StudentNurseryEn'],
        nurseryAr: j['StudentNurseryAr'],
        gradeEn: j['StudentGradeEn'],
        gradeAr: j['StudentGradeAr'],
        sectionEn: j['StudentSectionEn'],
        sectionAr: j['StudentSectionAr'],
      );

  static List<TagProfile> listFromJson(dynamic data) =>
      (data as List).map((e) => TagProfile.fromJson(e)).toList();
}

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/screens/account/Document/components/documentDetail.dart';
import 'package:seawork/screens/account/Document/components/documentTypebottomSheet.dart';
import 'package:seawork/screens/account/Document/model/documentModel.dart';
import 'package:seawork/screens/account/document/components/searchDoc.dart';
import 'package:seawork/screens/account/document/provider/documentProvider.dart';
import 'package:seawork/screens/account/profile/components/customTab.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/screens/account/profile/components/docItem.dart';
import 'package:seawork/screens/account/profile/components/filterDropDown.dart';
import 'package:seawork/utils/style/colors.dart';

final allDocumentsProvider = FutureProvider.autoDispose<MyDocumentModel>((
  ref,
) async {
  final repository = ref.read(documentRepositoryProvider);
  return await repository.getApplicationDocumentsDirectory();
});

final filteredDocumentsProvider = FutureProvider.autoDispose
    .family<MyDocumentModel, String?>((ref, status) async {
      final repository = ref.read(documentRepositoryProvider);
      return await repository.getApplicationDocumentsDirectory(status: status);
    });

final currentDocumentFilterProvider = StateProvider<String?>((ref) => null);

class MyDocument extends ConsumerStatefulWidget {
  const MyDocument({super.key});

  @override
  ConsumerState<MyDocument> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends ConsumerState<MyDocument>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearch = false;
  int? _expandedCardIndex;
  String _searchQuery = '';
  int _currentPage = 0;
  bool _hasMore = true;
  DioException? _networkError;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshCurrentTabData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _refreshCurrentTabData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _showDocumentUploadModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      isDismissible: true,
      enableDrag: true,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.85,
          expand: false,
          builder: (context, scrollController) {
            return ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              child: PrimaryScrollController(
                controller: scrollController,
                child: const DocumentUploadModal(),
              ),
            );
          },
        );
      },
    );
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      _expandedCardIndex = null;
      if (!_showSearch) {
        _searchController.clear();
        _searchQuery = '';
        _refreshCurrentTabData();
      }
    });
  }

  void _searchForDocumentType(String? type) {
    setState(() {
      _searchQuery = type ?? '';
      _currentPage = 0;
      _hasMore = true;
      _networkError = null;
      _expandedCardIndex = null;
    });
    _refreshCurrentTabData();
  }

  void _refreshCurrentTabData() {
    final filter = ref.read(currentDocumentFilterProvider);
    ref.invalidate(filteredDocumentsProvider(filter));
  }

  void _updateStatusFilter(String? status) {
    if (status == null) return;
    ref.read(currentDocumentFilterProvider.notifier).state = status;
    _refreshCurrentTabData();
  }

  String _formatDocumentType(String? documentType) {
    if (documentType == null) return 'Document';
    return documentType
        .replaceAll('Upload', '')
        .replaceAll('Request', '')
        .trim();
  }

  Widget _buildDocumentItem(Item item, BuildContext context, int index) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColors.boxshadowcolor.withOpacity(0.1),
            blurRadius: 9.6,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: GestureDetector(
        onTap: () {
          if (_expandedCardIndex == index) {
            _showDocumentDetailsModal(context, item);
          }
        },
        child: CardSectionTwo(
          title: _formatDocumentType(item.documentType),
          subtitle: item.dateTo != null ? _formatDate(item.dateTo!) : '',
          isSubtitleRed: _isDocumentExpired(item),
          showTrailing: true,
          trailingArrowType: "right_up",
          onTap: () {
            setState(() {
              if (_expandedCardIndex == index) {
                _expandedCardIndex = null;
              } else {
                _expandedCardIndex = index;
              }
            });
          },
          children:
              _expandedCardIndex == index
                  ? [
                    Padding0x8(const SizedBox()),
                    Divider(
                      thickness: 1,
                      indent: 9,
                      endIndent: 9,
                      color: AppColors.lightBlueBackgroundColor,
                    ),
                    Padding0x8(const SizedBox()),
                    DocumentItemWidget(
                      title: _formatDocumentType(item.documentType),
                      status: _getDocumentStatus(item),
                      screenWidth: MediaQuery.of(context).size.width,
                    ),
                  ]
                  : [],
        ),
      ),
    );
  }

  Widget _buildDocumentList(AsyncValue<MyDocumentModel> asyncDocs) {
    return asyncDocs.when(
      loading: () => const Center(child: CustomLoadingWidget()),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: AppColors.viewColor),
                const SizedBox(height: 16),
                OpenSansText(
                  error is DioException
                      ? (error.type == DioExceptionType.connectionError ||
                              error.type == DioExceptionType.connectionTimeout
                          ? 'No internet connection'
                          : 'Failed to load documents')
                      : 'An error occurred',
                  textAlign: TextAlign.center,
                  fontSize: 12,
                  color: AppColors.blackColor,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.viewColor,
                  ),
                  onPressed:
                      () => ref.refresh(
                        asyncDocs is AsyncValue<MyDocumentModel>
                            ? ref.read(currentDocumentFilterProvider) == null
                                ? allDocumentsProvider
                                : filteredDocumentsProvider(
                                  ref.read(currentDocumentFilterProvider),
                                )
                            : allDocumentsProvider,
                      ),
                  child: OpenSansText(
                    "Retry",
                    color: AppColors.whiteColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
      data: (documentModel) {
        final items = documentModel.items ?? [];
        final filteredDocuments =
            _searchQuery.isEmpty
                ? items
                : items
                    .where(
                      (item) =>
                          item.documentType.toLowerCase().contains(
                            _searchQuery.toLowerCase(),
                          ) ??
                          false,
                    )
                    .toList();

        if (filteredDocuments.isEmpty) {
          return _searchQuery.isEmpty
              ? Center(
                child: OpenSansText(
                  'No documents found',
                  fontSize: 14,
                  color: AppColors.blackColor,
                  fontWeight: FontWeight.w400,
                ),
              )
              : noSearchResultWidget(context);
        }

        return ListView.builder(
          shrinkWrap: true,
          itemCount: filteredDocuments.length,
          itemBuilder:
              (context, index) =>
                  _buildDocumentItem(filteredDocuments[index], context, index),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentFilter = ref.watch(currentDocumentFilterProvider);
    final documentsAsync =
        currentFilter == null
            ? ref.watch(allDocumentsProvider)
            : ref.watch(filteredDocumentsProvider(currentFilter));

    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          title: "My documents",
          showActionIcon: !documentsAsync.isLoading,
          onBackPressed: () => Navigator.pop(context),
        ),
        body: SafeArea(
          child: Padding20x0(
            Column(
              children: [
                Container(
                  height: 40,
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.whiteColor,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowBoxColor,
                        blurRadius: 9.6,
                        spreadRadius: 0,
                        offset: Offset.zero,
                      ),
                    ],
                  ),
                  child: CustomTab(tabController: _tabController),
                ),
                const SizedBox(height: 16),
                if (!documentsAsync.isLoading) ...[
                  Row(
                    children: [
                      const SizedBox(height: 45),
                      Expanded(
                        child: SearchBarDocWidget(
                          showSearchField: _showSearch,
                          searchController: _searchController,
                          hintText: "Search by type",
                          onSearch: _searchForDocumentType,
                          onClear: () {
                            setState(() {
                              _showSearch = false;
                            });
                          },
                          onToggleSearch: _toggleSearch,
                          filterWidget: FilterDropdownButton(
                            onFilterChanged: _updateStatusFilter,
                          ),
                          leadingIcon: CustomSvgImage(imageName: "search_icon"),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                ],
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Employee Tab
                      RefreshIndicator(
                        onRefresh: () async {
                          final filter = ref.read(
                            currentDocumentFilterProvider,
                          );
                          if (filter == null) {
                            await ref.refresh(allDocumentsProvider.future);
                          } else {
                            await ref.refresh(
                              filteredDocumentsProvider(filter).future,
                            );
                          }
                        },
                        child: _buildDocumentList(documentsAsync),
                      ),
                      // Parent Tab
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomSvgImage(imageName: 'nodata'),
                            const SizedBox(height: 12),
                            DmSansText(
                              'No data available',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.blackColor,
                            ),
                            const SizedBox(height: 8),
                            DmSansText(
                              'Entries will appear here once added',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.blackColor,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton:
            !documentsAsync.isLoading
                ? Padding(
                  padding: const EdgeInsets.only(bottom: 70.0),
                  child: FloatingActionButton(
                    backgroundColor: AppColors.viewColor,
                    onPressed: _showDocumentUploadModal,
                    shape: const CircleBorder(),
                    child: ClipOval(
                      child: CustomSvgImage(imageName: "add_icon"),
                    ),
                  ),
                )
                : null,
        bottomNavigationBar: CustomBottomNavigationBar(
          onTap: (int) {},
        ), // Always show bottom nav bar
      ),
    );
  }

  String _formatDate(dynamic date) {
    if (date is DateTime) {
      return DateFormat('yyyy-MM-dd').format(date);
    } else if (date is String) {
      try {
        final parsedDate = DateTime.parse(date);
        return DateFormat('yyyy-MM-dd').format(parsedDate);
      } catch (e) {
        return date;
      }
    }
    return 'Invalid date';
  }

  bool _isDocumentExpired(Item item) {
    if (item.dateTo == null) return false;

    DateTime? expiryDate;
    if (item.dateTo is DateTime) {
      expiryDate = item.dateTo as DateTime;
    } else if (item.dateTo is String) {
      try {
        expiryDate = DateTime.parse(item.dateTo as String);
      } catch (e) {
        return false;
      }
    }

    return expiryDate?.isBefore(DateTime.now()) ?? false;
  }

  String _getDocumentStatus(Item item) {
    if (item.dateTo == null) return '';

    DateTime? expiryDate;
    if (item.dateTo is DateTime) {
      expiryDate = item.dateTo as DateTime;
    } else if (item.dateTo is String) {
      try {
        expiryDate = DateTime.parse(item.dateTo as String);
      } catch (e) {
        return '';
      }
    }

    return expiryDate?.isBefore(DateTime.now()) ?? false ? 'Expired' : '';
  }

  void _showDocumentDetailsModal(BuildContext context, Item item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DocumentDetailsModal(item: item),
    );
  }
}




import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
// import 'package:seawork/screens/dashboard/dashboard/dashBoard.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:go_router/go_router.dart';

class ReimbursementScreen extends StatefulWidget {
  const ReimbursementScreen({super.key});

  @override
  State<ReimbursementScreen> createState() => _ReimbursementScreenState();
}

class _ReimbursementScreenState extends State<ReimbursementScreen> {
  final _formKey = GlobalKey<FormState>();
  String? selectedType;
  bool isBottomSheetOpen = false;

  final List<String> ReimbursementTypes = [
    "Training or conference reimbursement",
    "Transportation reimbursement",
    "Visa expense reimbursement",
    "Shipping reimbursement",
    "General reimbursement",
    "Medical bill reimbursement",
  ];

  final InputDecoration textFieldDecoration = InputDecoration(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.lightGreyshade),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: AppColors.lightGreyColor2),
    ),
    contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
  );
  void _showLoanTypeBottomSheet() async {
    setState(() {
      isBottomSheetOpen = true;
    });

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        final itemHeight = 56.0;
        final headerHeight = 40.0;
        final bottomPadding = MediaQuery.of(context).padding.bottom;
        final totalHeight =
            (itemHeight * ReimbursementTypes.length) +
            headerHeight +
            bottomPadding +
            16.0;

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [CustomSvgImage(imageName: 'Rectangle12231')],
              ),
              const SizedBox(height: 8),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      for (var type in ReimbursementTypes)
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedType = type;
                            });
                            Navigator.pop(context);
                            _navigateToReimbursementDetails();
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.only(top: 8.0),
                            decoration: const BoxDecoration(
                              color: Colors.transparent,
                            ),
                            child: Container(
                              height: 40,
                              padding: const EdgeInsets.symmetric(
                                vertical: 9.0,
                              ),
                              margin: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    selectedType == type
                                        ? AppColors.lightGreyColor2
                                        : AppColors.transparentColor,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              alignment: Alignment.center,
                              child: OpenSansText(
                                type,
                                textAlign: TextAlign.center,
                                fontSize: 14,
                                fontWeight:
                                    selectedType == type
                                        ? FontWeight.w600
                                        : FontWeight.w400,
                                color: AppColors.blackColor,
                                letterSpacing: 0,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: bottomPadding),
            ],
          ),
        );
      },
    );

    setState(() {
      isBottomSheetOpen = false;
    });
  }

  void _navigateToReimbursementDetails() {
    if (selectedType != null) {
      context.push(
        '/reimbursement-advance-form',
        extra: {'reimbursementTypes': selectedType!.trim()},
      );
    }
  }

  void _navigateToDashboard() {
    Navigator.pop(context);
  }

  Future<bool> _onWillPop() async {
    _navigateToDashboard();
    return false; // Prevents default back behavior
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
              canPop: true,
              onPopInvoked: (didPop) {
                if (!didPop) {
                  context.pop();
                }
              },
              child:Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: AppBar(
          backgroundColor: AppColors.secondaryColor,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const CustomSvgImage(imageName: 'appbackbutton'),
            onPressed: _navigateToDashboard,
          ),
          title: Center(
            child: DmSansText(
              'Reimbursement request',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.viewColor,
            ),
          ),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: _navigateToDashboard,
                child: Row(
                  children: [
                    CustomSvgImage(imageName: 'Vector'),
                    SizedBox(width: 4),
                  ],
                ),
              ),
            ),
          ],
          elevation: 0,
        ),
        body: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                HeadingText(
                  text: 'Reimbursement request type',
                  hasAsterisk: true,
                ),
                const SizedBox(height: 10),
                GestureDetector(
                  onTap: _showLoanTypeBottomSheet,
                  child: Container(
                    height: 43,
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20, right: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          OpenSansText(
                            selectedType ?? 'Select type',
                            fontSize: 14,
                            color: AppColors.blackColor,
                          ),
                          CustomSvgImage(
                            imageName:
                                isBottomSheetOpen ? "Arrowup" : "ArrowLeft",
                            color: AppColors.viewColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

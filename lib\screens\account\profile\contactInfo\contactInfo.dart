import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/account/profile/components/personInfoWidget.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';
import 'package:seawork/screens/account/profile/personalInfo/components/userPermission.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/utils/style/colors.dart';

class ContactInfo extends ConsumerStatefulWidget {
  const ContactInfo({Key? key}) : super(key: key);

  @override
  ConsumerState<ContactInfo> createState() => _ContactInfoState();
}

class _ContactInfoState extends ConsumerState<ContactInfo> {
  String? selectedFieldDemographic;
  EmployeeDetailModel? employeeDetails;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissionsAndFetchData();
  }

  Future<void> _checkPermissionsAndFetchData() async {
    final userProfile = await ref.read(userProfileProvider.future);
    final userPermission = UserPermission(userProfile);

    if (!userPermission.hasEMSAccess && mounted) {
      Navigator.of(context).pop();
      return;
    }

    _fetchEmployeeDetails();
  }

  Future<void> _fetchEmployeeDetails() async {
    setState(() => isLoading = true);
    final profileRepository = ref.read(profileRepositoryProvider);
    final details = await profileRepository.getProfileDetails();

    if (mounted) {
      setState(() {
        employeeDetails = details;
        isLoading = false;
      });
    }
  }

  void _onFieldTapped(String label) {
    setState(() {
      selectedFieldDemographic = label;
    });
  }

  void _onItemTapped(int index) {
    ref.read(bottomNavBarProvider.notifier).updateIndex(index);
  }

  Item? get _firstItem {
    return employeeDetails?.items?.isNotEmpty == true
        ? employeeDetails?.items?.first
        : null;
  }

  @override
  Widget build(BuildContext context) {
    return ref
        .watch(userProfileProvider)
        .when(
          loading:
              () => const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              ),
          error:
              (error, stack) =>
                  Scaffold(body: Center(child: Text('Error: $error'))),
          data: (userProfile) {
            final userPermission = UserPermission(userProfile);

            if (!userPermission.hasEMSAccess) {
              return const SizedBox.shrink();
            }

         return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:Scaffold(
              backgroundColor: AppColors.secondaryColor,
              appBar: CustomAppBar(
                title: "Contact info",
                showActionIcon: true,
                onBackPressed: () {
                  context.pop();
                },
              ),
              body: SafeArea(child: _buildContactInfoContent()),
              bottomNavigationBar: CustomBottomNavigationBar(
                onTap: _onItemTapped,
              ),
          ) );
          },
        );
  }

  Widget _buildContactInfoContent() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.viewColor),
      );
    }

    return Padding16x0(
      Column(
        children: [
          Padding0x16(
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.whiteColor,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadowcolor.withOpacity(0.25),
                    blurRadius: 9.6,
                    spreadRadius: 0,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding20x0(
                    Padding0x8(
                      DMSans700Large(
                        16,
                        'Communication',
                        AppColors.blackColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Padding20x0(
                    const Divider(
                      height: 1,
                      color: AppColors.lightBlueBackgroundColor,
                    ),
                  ),
                  if (_firstItem != null) ...[
                    const SizedBox(height: 20),
                    SelectableRow(
                      label: "Work email",
                      value: _firstItem?.workEmail ?? '',
                      isSelected: selectedFieldDemographic == 'Work email',
                      onTap: _onFieldTapped,
                    ),
                    SelectableRow(
                      label: "Home address",
                      value: _firstItem?.addressLine1 ?? 'Null',
                      isSelected: selectedFieldDemographic == 'Home address',
                      onTap: _onFieldTapped,
                      bottomPadding: 8,
                      subText: _firstItem?.city ?? '',
                    ),
                    const SizedBox(height: 20),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Existing providers remain unchanged
final bottomNavBarProvider = StateNotifierProvider<BottomNavBarNotifier, int>(
  (ref) => BottomNavBarNotifier(),
);

class BottomNavBarNotifier extends StateNotifier<int> {
  BottomNavBarNotifier() : super(3);
  void updateIndex(int index) => state = index;
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/screens/service/ticket/models/serviceItem.dart';
import 'package:seawork/screens/service/ticket/providers/ticketProviders.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'dart:convert';

import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:flutter_svg/svg.dart';

// Provider for submit button state
final isSubmitButtonActiveProvider = StateProvider<bool>((ref) => false);
// Provider for description text
final descriptionTextProvider = StateProvider<String>((ref) => '');
// Add this provider at the top with other providers
final isSubmittingProvider = StateProvider<bool>((ref) => false);

class RequestAndReport extends ConsumerWidget {
  final String iconClicked;
  final String? requestType;
  final String? prefilledDescription;
  final int serviceCategoryId;
  final int itilTicketTypeId;

  const RequestAndReport({
    Key? key,
    required this.iconClicked,
    required this.itilTicketTypeId,
    required this.serviceCategoryId,
    this.requestType,
    this.prefilledDescription,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the service categories provider with parent_id=59
    // final serviceCategoriesAsync = ref.watch(serviceCategoriesProvider(59));
    final serviceItemsAsync = ref.watch(
      serviceItemsProvider(serviceCategoryId),
    );

    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
      backgroundColor: const Color(0xFFF7FDFF),
      body: Column(
        children: [
          SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            color: const Color(0xFFF7FDFF),
            height: 56,
            child: Row(
              children: [
                IconButton(
                  icon: Image.asset(
                    "assets/images/Group.png",
                    width: 24,
                    height: 24,
                  ),
                  onPressed: () {
                    if (requestType != null) {
                      Navigator.pop(context);
                    } else {
                      // Navigator.pushReplacement(
                      //   context,
                      //   MaterialPageRoute(
                      //     builder: (context) => Help(iconClicked: 'Help'),
                      //   ),
                      // );
                      Navigator.pop(context);
                    }
                  },
                ),
                const Spacer(),
                Text(
                  iconClicked,
                  style: const TextStyle(
                    fontSize: 16,
                    fontFamily: 'DM Sans',
                    fontWeight: FontWeight.w600,
                    color: Color.fromARGB(255, 57, 80, 98),
                  ),
                ),
                const Spacer(flex: 2),
              ],
            ),
          ),
          const SizedBox(height: 40),
          serviceItemsAsync.when(
            data:
                (categories) => Expanded(
                  child: ListView.builder(
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24.0,
                          vertical: 6.0,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: const [
                              BoxShadow(
                                color: AppColors.boxshadow,
                                blurRadius: 9.6,
                                offset: Offset(0, 0),
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Material(
                            color: AppColors.transparentColor,
                            borderRadius: BorderRadius.circular(8),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(8),
                              splashColor: AppColors.microinteraction,
                              highlightColor: AppColors.microinteraction,
                              onTap:
                                  () => _showRequestBottomSheet(
                                    context,
                                    category,
                                    ref,
                                  ),
                              child: SizedBox(
                                height: 71,
                                child: Row(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        left: 16.0,
                                      ),
                                      child: SvgPicture.asset(
                                        _getServiceIcon(category.id ?? 0),
                                        width: 44,
                                        height: 45,
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Expanded(
                                      child: DMSans600Medium(
                                        14,
                                        category.name ?? "",
                                        AppColors.blackColor,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        height: 18.23 / 14,
                                        letterSpacing: 0,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error:
                (error, stack) =>
                    Center(child: Text('Error loading services: $error')),
          ),
        ],
      ),
     ) );
  }

  void _showRequestBottomSheet(
    BuildContext context,
    ServiceItem category,
    WidgetRef ref,
  ) {
    List<FileModel> uploadedFiles = [];
    print((category.name ?? "") + " is the name of the category");
    // Check if we came from search
    final bool fromSearch = this.requestType != null;

    // Create a persistent TextEditingController
    final TextEditingController descriptionController = TextEditingController();

    // Pre-set the description text if provided
    if (prefilledDescription != null) {
      descriptionController.text = prefilledDescription!;
      ref.read(descriptionTextProvider.notifier).state = prefilledDescription!;
      ref.read(isSubmitButtonActiveProvider.notifier).state = true;
    }

    // Show the bottom sheet

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        final rootContext = context;
        return Consumer(
          builder: (context, ref, _) {
            final serviceItemDetails = ref.watch(
              serviceItemByIdProvider(category.id ?? 0),
            );
            return serviceItemDetails.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(child: Text('Error: $error')),
              data: (details) {
                final detailRequestDetails =
                    details.serviceRequestDetails?.isNotEmpty == true
                        ? details.serviceRequestDetails?.first
                        : null;

                if (detailRequestDetails == null) {
                  return const Center(
                    child: Text('No ticket type details available'),
                  );
                }

                final requestTypeId =
                    detailRequestDetails.servicedetailRequesttypeid;

                final ticketTypeDetails = ref.watch(
                  ticketTypeByIdProvider(requestTypeId!),
                );

                return ticketTypeDetails.when(
                  loading:
                      () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(child: Text('Error: $error')),
                  data: (ticketType) {
                    if (ticketType.id == 0) {
                      return Container(
                        height: MediaQuery.of(context).size.height * 0.94,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(24),
                              child: const Center(
                                child: Text(
                                  'This ticket type is not found for creation',
                                ),
                              ),
                            ),
                            const Spacer(),
                          ],

                          // Submit button
                        ),
                      );
                    }

                    var fields = ticketType.fields;

                    if (fields == null || fields.isEmpty) {
                      return const Center(
                        child: Text('No fields available for this ticket type'),
                      );
                    }
                    print("Fields: $fields");

                    var mandatoryFields = ["Summary", "Details", "Agent"];

                    var mandatoryFieldLabels =
                        fields.where((field) {
                          return mandatoryFields.contains(
                            field.fieldinfo?.label,
                          );
                        }).toList();

                    print("Mandatory Fields: $mandatoryFieldLabels");

                    if (mandatoryFieldLabels.isEmpty) {
                      return Container(
                        height: MediaQuery.of(context).size.height * 0.94,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(24),
                              child: const Center(
                                child: Text(
                                  'This ticket type requires more fields than we expect, please do via web',
                                ),
                              ),
                            ),
                            const Spacer(),
                          ],

                          // Submit button
                        ),
                      );
                    }

                    return Container(
                      height: MediaQuery.of(context).size.height * 0.94,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Handle bar at the top
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.only(top: 12),
                              child: Container(
                                width: 40,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE8E8E8),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            ),
                          ),
                          // Back button and title
                          Padding(
                            padding: const EdgeInsets.all(24),
                            child: Row(
                              children: [
                                Expanded(
                                  child: DmSansText(
                                    category.name,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.headingColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Form fields
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                BuildTextField(
                                  label: 'Describe the issue',
                                  maxLines: 4,
                                  controller: descriptionController,
                                  onChanged: (value) {
                                    ref
                                        .read(descriptionTextProvider.notifier)
                                        .state = value;
                                    // Enable submit button if description is not empty
                                    ref
                                        .read(
                                          isSubmitButtonActiveProvider.notifier,
                                        )
                                        .state = value.isNotEmpty;
                                  },
                                ),
                                const SizedBox(height: 30),
                                HeadingText(text: 'Attachments'),
                                SizedBox(height: 8),
                                AttachmentField(
                                  uploadedFiles: uploadedFiles,
                                  onFilesChanged: (p0) {},
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          // Submit button
                          Padding(
                            padding: const EdgeInsets.all(24),
                            child: Consumer(
                              builder: (context, ref, child) {
                                final isSubmitButtonActive = ref.watch(
                                  isSubmitButtonActiveProvider,
                                );
                                final isSubmitting = ref.watch(
                                  isSubmittingProvider,
                                );

                                return ElevatedButton(
                                  onPressed:
                                      isSubmitting || !isSubmitButtonActive
                                          ? null
                                          : () async {
                                            ref
                                                .read(
                                                  isSubmittingProvider.notifier,
                                                )
                                                .state = true;
                                            try {
                                              final description = ref.read(
                                                descriptionTextProvider,
                                              );
                                              final attachments =
                                                  uploadedFiles.map((file) {
                                                    return {
                                                      "filename": file.name,
                                                      "data_base64":
                                                          "data:${file.mimeType};base64,${base64Encode(file.bytes)}",
                                                      "tickettype_id":
                                                          requestTypeId,
                                                      "allow_anon_upload":
                                                          false,
                                                      "_uploading": false,
                                                      "showforusers": true,
                                                      "datecreated":
                                                          DateTime.now()
                                                              .toIso8601String(),
                                                    };
                                                  }).toList();

                                              try {
                                                // Check if the attachments are empty and set to null if so

                                                final result = await ref.read(
                                                  createTicketProvider((
                                                    details: description,
                                                    summary:
                                                        category.name ?? "",
                                                    attachments:
                                                        attachments.isNotEmpty
                                                            ? attachments
                                                            : null,
                                                    ticketTypeId: requestTypeId,
                                                  )).future,
                                                );

                                                print("Result: $result");

                                                if (result) {
                                                  uploadedFiles.clear();
                                                  ref
                                                      .read(
                                                        descriptionTextProvider
                                                            .notifier,
                                                      )
                                                      .state = '';
                                                  ref.refresh(
                                                    ticketsProvider(
                                                      'All tickets',
                                                    ),
                                                  );

                                                  // If coming from search, go back to search page after success
                                                  if (fromSearch) {
                                                    Navigator.of(
                                                      context,
                                                    ).pop(); // Close bottom sheet
                                                    Navigator.of(
                                                      context,
                                                    ).pop(); // Back to search
                                                  } else {
                                                    // Normal flow - go to Help page
                                                    Navigator.of(
                                                      context,
                                                    ).popUntil(
                                                      (route) => route.isFirst,
                                                    );

                                                    //Navigator.of(context).pop();
                                                    // Navigator.pushReplacement(
                                                    //   context,
                                                    //   MaterialPageRoute(
                                                    //     builder: (context) =>
                                                    //         Help(iconClicked: 'Help'),
                                                    //   ),
                                                    // );
                                                  }
                                                }
                                              } catch (e) {
                                                print("ErrorInCreation: $e");
                                                ScaffoldMessenger.of(
                                                  rootContext,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      'Failed to create ticket ' +
                                                          (e.toString()),
                                                    ),
                                                    backgroundColor: Colors.red,
                                                  ),
                                                );
                                              }
                                            } finally {
                                              ref
                                                  .read(
                                                    isSubmittingProvider
                                                        .notifier,
                                                  )
                                                  .state = false;
                                            }
                                          },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF395062),
                                    minimumSize: const Size(
                                      double.infinity,
                                      56,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child:
                                      isSubmitting
                                          ? const CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          )
                                          : DmSansText(
                                            'Submit request',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color:
                                                isSubmitButtonActive
                                                    ? Colors.white
                                                    : AppColors.lightGreyColor,
                                          ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    ).then((_) {
      // If dismissed by user and came from search, go back to search
      // Don't navigate back if not from search
      if (fromSearch) {
        Navigator.of(context).pop();
      }
    });
  }

  Widget _buildTextField(
    String label, {
    int maxLines = 1,
    void Function(String)? onChanged,
    TextEditingController? controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DmSansText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.blackColor,
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE8E8E8)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE8E8E8)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF062540)),
            ),
          ),
        ),
      ],
    );
  }

  int _getTicketTypeId(String requestType) {
    switch (requestType) {
      case 'IT service request':
        return 23;
      case 'Facilities request':
        return iconClicked == "Report an incident" ? 41 : 44;
      case 'Translation request':
        return 53;
      case 'Academic request':
        return 57;
      default:
        return 23;
    }
  }

  String _getServiceIcon(int serviceId) {
    switch (serviceId) {
      case 13:
        return 'assets/images/ITservicereq.svg';
      case 14:
        return 'assets/images/Facilitiesreq.svg';
      case 40:
        return 'assets/images/Academicreq.svg';
      case 47:
        return 'assets/images/Translationalreq.svg';
      default:
        return 'assets/images/ITservicereq.svg';
    }
  }
}

class BuildTextField extends StatelessWidget {
  final String label;
  final int maxLines;
  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final String? hintText;

  const BuildTextField({
    Key? key,
    required this.label,
    this.maxLines = 1,
    this.controller,
    this.onChanged,
    this.hintText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DmSansText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.blackColor,
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          onChanged: onChanged,
          style: GoogleFonts.openSans(
            color: AppColors.blackColor,
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: GoogleFonts.openSans(
              color: AppColors.lightGreyshade,
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGreyColor2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGreyColor2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGreyColor2),
            ),
            filled: true,
            fillColor: AppColors.whiteColor,
          ),
        ),
      ],
    );
  }
}

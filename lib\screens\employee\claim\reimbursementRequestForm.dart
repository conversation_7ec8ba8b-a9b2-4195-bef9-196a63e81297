import 'package:flutter/material.dart';
import 'package:seawork/screens/employee/claim/components/reimbursementform.dart';
import 'package:seawork/screens/employee/claim/components/claimtype.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/utils/style/colors.dart';

class ReimbursementAdvanceForm extends StatefulWidget {
  final String reimbursementTypes;
  final bool showAppBar;
  final bool showClaimTypeDropdown;
  final bool showReimbursementFormDropdown;
  final String? initialClaimType;

  const ReimbursementAdvanceForm({
    Key? key,
    required this.reimbursementTypes,
    this.showAppBar = true,
    this.showClaimTypeDropdown = false,
    this.showReimbursementFormDropdown = true,
    this.initialClaimType,
  }) : super(key: key);

  @override
  State<ReimbursementAdvanceForm> createState() =>
      _ReimbursementAdvanceFormState();
}

class _ReimbursementAdvanceFormState extends State<ReimbursementAdvanceForm> {
  String? currentClaimType;

  @override
  void initState() {
    super.initState();
    currentClaimType = widget.initialClaimType ?? widget.reimbursementTypes;
  }

  void _showModalSheet(BuildContext context, Widget child) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => child,
    );
  }

  @override
  Widget build(BuildContext context) {
  return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title:
            widget.showClaimTypeDropdown
                ? 'Add claims'
                : 'Reimbursement request',
        showActionIcon: true,
      ),
      body: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight:
                MediaQuery.of(context).size.height -
                (widget.showAppBar ? kToolbarHeight : 0) -
                MediaQuery.of(context).padding.top -
                MediaQuery.of(context).padding.bottom,
          ),
          child: IntrinsicHeight(
            child: Column(
              children: [
                if (widget.showClaimTypeDropdown)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20.0,
                      vertical: 1.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 2),
                        HeadingText(text: "Claim type", hasAsterisk: true),
                        SizedBox(height: 8),
                        ClaimTypeSelector(
                          initialClaimType: currentClaimType,
                          onClaimTypeChanged: (newClaimType) {
                            setState(() {
                              currentClaimType = newClaimType;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                Expanded(
                  child: ReimbursementForm(
                    key: ValueKey(
                      currentClaimType,
                    ), // Unique key to force complete rebuild
                    showDropdown: widget.showReimbursementFormDropdown,
                    reimbursementType:
                        currentClaimType ?? widget.reimbursementTypes,
                    onSubmit: (formData) {
                      print('Form submitted with data:');
                      print('Course Name: ${formData.courseName}');
                      print('From Date: ${formData.fromDate}');
                      print('To Date: ${formData.toDate}');
                      print('Venue: ${formData.venue}');
                      print('Cost: ${formData.cost}');
                      print('Year of Claim: ${formData.yearOfClaim}');
                      print('Shipped From: ${formData.shippedFrom}');
                      print('Weight: ${formData.weight}');
                      print('Expense Type: ${formData.expenseType}');
                      print('Payment Mode: ${formData.paymentMode}');
                      print('Amount: ${formData.amount}');
                      print('Links: ${formData.links}');
                      print('Comments: ${formData.comments}');
                      print('Attachments: ${formData.attachments.length}');
                      print('Justification: ${formData.justification}');
                      print(
                        'Reimbursement Type: ${formData.reimbursementType}',
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
   ) );
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customIcon.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customInfoCard.dart';
import 'package:seawork/components/widget/customPButton.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MyAccountScreen extends StatefulWidget {
  @override
  State<MyAccountScreen> createState() => _MyAccountScreenState();
}

class _MyAccountScreenState extends State<MyAccountScreen>
    with WidgetsBindingObserver {
  int _selectedIndex = 3;
  List<String> cards = ['Employee'];
  String? selectedCard;
  bool showAddButton = true;
  int cardAddCount = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _resetToDefaultState();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused) {
      _resetToDefaultState();
    }
  }

  void _resetToDefaultState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('added_cards', ['Employee']);
  }

  void _onItemTapped(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      _resetToDefaultState();
    }
  }

  // Future<void> _addCard() async {
  //   setState(() {
  //     cardAddCount++;

  //     if (cardAddCount == 1) {
  //       cards.add('Parent');
  //     } else if (cardAddCount == 2) {
  //       cards.add('Vendor');
  //       showAddButton = false;
  //     }
  //   });
  // }

  BoxDecoration _cardDecoration() {
    return BoxDecoration(
      color: AppColors.whiteColor,
      borderRadius: BorderRadius.circular(8),

      boxShadow: [
        BoxShadow(
          color: AppColors.boxshadow,
          blurRadius: 4.6,
          spreadRadius: 0,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
 
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        body: Padding16x16(
          Column(
            children: [
              CustomAppBar(
                title: "My account",
                // showActionIcon: true,
                onBackPressed: () {
                  _resetToDefaultState();
                  Navigator.pop(context);
                },
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    if (cards.contains('Employee'))
                      Container(
                        decoration: _cardDecoration(),

                        child: CardSectionTwo(
                          shadowColor: Colors.transparent,
                          title: "Employee",
                          subtitle: "View employee profile details",
                          icon: const CustomIcon(
                            imagePath: "assets/images/acct_icon.svg",
                          ),
                          showTrailing: true,
                          trailingArrowType: "right_up",
                          children: [
                            Padding8x0(
                              ContactInfoCard(
                                title: "Personal info",
                                subtitle: "View your personal info",
                                onTap: () {
                                  context.push('/personal-info');
                                },
                              ),
                            ),
                            Padding8x0(
                              ContactInfoCard(
                                title: "Contact info",
                                subtitle: "View your contact info",
                                onTap: () {
                                  context.push('/contact-info');
                                },
                              ),
                            ),
                            Padding8x0(
                              ContactInfoCard(
                                title: "Employment info",
                                subtitle: "View your employment info",
                                onTap: () {
                                  context.push('/employment-info');
                                },
                              ),
                            ),
                            SizedBox(height: 8),
                          ],
                        ),
                      ),

                    if (cards.contains('Parent')) Padding0x8(SizedBox()),
                    if (cards.contains('Parent'))
                      Container(
                        decoration: _cardDecoration(),
                        child: CardSectionTwo(
                          shadowColor: Colors.transparent,
                          title: "Parent",
                          subtitle: "View parent profile details",
                          icon: const CustomIcon(
                            imagePath: "assets/images/acct_icon.svg",
                          ),
                          children: [],
                        ),
                      ),
                    if (cards.contains('Vendor')) Padding0x8(SizedBox()),
                    if (cards.contains('Vendor'))
                      Container(
                        decoration: _cardDecoration(),
                        child: CardSectionTwo(
                          shadowColor: Colors.transparent,
                          title: "Vendor",
                          subtitle: "View vendor profile details",
                          icon: const CustomIcon(
                            imagePath: "assets/images/acct_icon.svg",
                          ),
                          children: [],
                        ),
                      ),
                    Padding0x16(SizedBox()),
                    if (showAddButton)
                      Container(
                        decoration: _cardDecoration(),
                        child: AddProfileButton(
                          text: "Add profile",
                          onTap: () {},
                          //_addCard,  // Commented out to disable the add button functionality
                          showIcon: true,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: CustomBottomNavigationBar(onTap: _onItemTapped),
      ),
    );
  }
}

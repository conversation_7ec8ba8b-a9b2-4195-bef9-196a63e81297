import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/countryDropDown.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customDropDown.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/account/document/components/docCountrySelector.dart';
import 'package:seawork/screens/employee/letterRequest/noc.dart';
import 'package:seawork/utils/style/colors.dart';

final motherCountryProvider = StateProvider<String?>((ref) => null);
final fatherCountryProvider = StateProvider<String?>((ref) => null);

class DocumentFormFields extends ConsumerStatefulWidget {
  final String? selectedDocumentType;
  final String? selectedOption;
  final String? selectedVaccineStatus;
  final TextEditingController personNameController;
  final TextEditingController passportNumberController;
  final TextEditingController visaIssueDateController;
  final TextEditingController visaLocationController;
  final TextEditingController issuingAuthorityController;
  final TextEditingController visaExpiryDateController;
  final TextEditingController accountNumberController;
  final TextEditingController ibanNumberController;
  final TextEditingController bankNumberController;
  final TextEditingController branchNameController;
  final TextEditingController bisCodeController;
  final TextEditingController vaccineDateController;
  final TextEditingController dateOfBirthController;
  final TextEditingController placeOfBirthController;
  final TextEditingController emiratesIdDateController;
  final TextEditingController emiratesIdNumberController;
  final TextEditingController residencyVisaUidController;
  final TextEditingController fileNumberController;
  final TextEditingController nameOfMotherController;
  final TextEditingController nameOfFatherController;
  final TextEditingController issuingCommentsController;
  final TextEditingController certificateNameController;
  final TextEditingController certificateNumberController;
  final TextEditingController certificateFromDateController;
  final TextEditingController certificateToDateController;
  final TextEditingController certificateIssuedOnController;
  final TextEditingController certificateIssuingLocationController;
  final TextEditingController certificateIssuingAuthorityController;
  final TextEditingController certificateIssuingCommentsController;
  final TextEditingController? visaNumberController;
  final TextEditingController dateController;
  final TextEditingController Numbercontroller;
  final Function(String?) onOptionChanged;
    final TextEditingController countryController;


  const DocumentFormFields({
    Key? key,
    required this.countryController,
    required this.selectedDocumentType,
    required this.selectedOption,
    required this.selectedVaccineStatus,
    required this.personNameController,
    required this.passportNumberController,
    required this.visaIssueDateController,
    required this.visaLocationController,
    required this.issuingAuthorityController,
    required this.visaExpiryDateController,
    required this.accountNumberController,
    required this.ibanNumberController,
    required this.bankNumberController,
    required this.branchNameController,
    required this.bisCodeController,
    required this.vaccineDateController,
    required this.dateOfBirthController,
    required this.placeOfBirthController,
    required this.emiratesIdDateController,
    required this.emiratesIdNumberController,
    required this.residencyVisaUidController,
    required this.fileNumberController,
    required this.nameOfMotherController,
    required this.nameOfFatherController,
    required this.issuingCommentsController,
    required this.certificateNameController,
    required this.certificateNumberController,
    required this.certificateFromDateController,
    required this.certificateToDateController,
    required this.certificateIssuedOnController,
    required this.certificateIssuingLocationController,
    required this.certificateIssuingAuthorityController,
    required this.certificateIssuingCommentsController,
    required this.Numbercontroller,
    this.visaNumberController,
    required this.dateController,
    required this.onOptionChanged,
  }) : super(key: key);

  @override
  ConsumerState<DocumentFormFields> createState() => _DocumentFormFieldsState();
}

class _DocumentFormFieldsState extends ConsumerState<DocumentFormFields> {
  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  DateTime? visaIssueDate;
  DateTime? visaExpiryDate;
  DateTime? certificateFromDate;
  DateTime? certificateToDate;
  DateTime? emiratesIdIssueDate;
  DateTime? passportIssueDate;

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) return 'This field is required';
    if (!RegExp(r'^[a-zA-Z ]+$').hasMatch(value))
      return 'Only alphabetic characters are allowed';
    if (value.length > 50) return 'Maximum 50 characters allowed';
    return null;
  }

  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) return 'This field is required';
    if (!RegExp(r'^[0-9]+$').hasMatch(value)) return 'Only numbers are allowed';
    if (value.length > 50) return 'Maximum 50 digits allowed';
    return null;
  }

  String? _validateAlphaNumeric(String? value) {
    if (value == null || value.isEmpty) return 'This field is required';
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value))
      return 'Only alphanumeric characters are allowed';
    if (value.length > 50) return 'Maximum 50 characters allowed';
    return null;
  }

  String? _validateOptionalAlphaNumeric(String? value) {
    if (value != null && value.isNotEmpty) {
      if (!RegExp(r'^[a-zA-Z0-9 ]*$').hasMatch(value))
        return 'Only alphanumeric characters are allowed';
      if (value.length > 50) return 'Maximum 50 characters allowed';
    }
    return null;
  }

  String? _validateComments(String? value) {
    if (value != null && value.length > 500)
      return 'Maximum 500 characters allowed';
    return null;
  }

  String? _validateDate(String? value) {
    if (value == null || value.isEmpty) return 'This field is required';
    return null;
  }

  // Date validation methods
  void _validateVisaExpiryDate(DateTime? date) {
    if (visaIssueDate != null &&
        date != null &&
        date.isBefore(visaIssueDate!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Expiry date cannot be earlier than issue date'),
          backgroundColor: AppColors.red,
        ),
      );
      widget.visaExpiryDateController.clear();
      return;
    }
    setState(() {
      visaExpiryDate = date;
    });
  }

  void _validateCertificateToDate(DateTime? date) {
    if (certificateFromDate != null &&
        date != null &&
        date.isBefore(certificateFromDate!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('To date cannot be earlier than From date'),
          backgroundColor: AppColors.red,
        ),
      );
      widget.certificateToDateController.clear();
      return;
    }
    setState(() {
      certificateToDate = date;
    });
  }

  void _validateFutureDate(DateTime? date) {
    if (date != null && date.isAfter(DateTime.now())) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Future dates are not allowed'),
          backgroundColor: AppColors.red,
        ),
      );
      widget.vaccineDateController.clear();
      widget.dateOfBirthController.clear();
      return;
    }
  }


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        Navigator.pop(context);
      },
      child: Column(
        children: [
          if (widget.selectedDocumentType !=
                  'Upload Attested University Certificates' &&
              widget.selectedDocumentType != 'Upload Certificate Equivalency' &&
              widget.selectedDocumentType != 'Upload Experience Letter' &&
              widget.selectedDocumentType !=
                  'Upload Police Clearance Certificate' &&
              widget.selectedDocumentType !=
                  'Upload Spouse\'s Detailed Salary Certificate' &&
              widget.selectedDocumentType != 'Upload University Certificate' &&
              widget.selectedDocumentType != 'Upload Marriage Certificate' &&
              widget.selectedDocumentType != 'Upload Work Medical Fitness' &&
              widget.selectedDocumentType != 'Upload UAE Family Book' &&
              widget.selectedDocumentType != 'Upload COVID PCR Report' &&
              widget.selectedDocumentType !=
                  'Upload New Bank Account Details' &&
              widget.selectedDocumentType != 'Upload Vaccine Status')
            CustomTextFieldWithHeading(
              Heading: "Person's name",
              hasAsterisk: true,
              hintText: 'Enter person name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.personNameController,
              validateLettersOnly: true,
              maxLength: 50,
            ),

          if (widget.selectedDocumentType == 'Upload COVID PCR Report') ...[
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Latest vaccine date',
              hintText: 'Select date',
              controller: widget.vaccineDateController,
              onDateSelected: _validateFutureDate,
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
            const SizedBox(height: 24),
            HeadingText(text: 'Test result'),
            SizedBox(height: MediaQuery.of(context).size.height * 0.03),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<String>(
                      value: 'Yes',
                      groupValue: widget.selectedOption,
                      onChanged: widget.onOptionChanged,
                      activeColor: AppColors.viewColor,
                    ),
                    const SizedBox(width: 4),
                    OpenSansText(
                      'Yes',
                      color: AppColors.blackColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<String>(
                      value: 'No',
                      groupValue: widget.selectedOption,
                      onChanged: widget.onOptionChanged,
                      activeColor: AppColors.viewColor,
                    ),
                    const SizedBox(width: 4),
                    OpenSansText(
                      'No',
                      color: AppColors.blackColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
              ],
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Emirates ID') ...[
            CustomTextFieldWithHeading(
              Heading: 'Emirates ID number',
              hasAsterisk: true,
              hintText: 'Enter Emirates ID number',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.emiratesIdNumberController,
              validateAlphanumericOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Date of issue',
              hintText: 'Select date',
              controller: widget.emiratesIdDateController,
              onDateSelected: (date) {
                setState(() {
                  emiratesIdIssueDate = date;
                });
              },
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Residency Visa') ...[
            CustomTextFieldWithHeading(
              Heading: 'Residency visa UID',
              hasAsterisk: true,
              hintText: 'Enter residency visa UID',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.residencyVisaUidController,
              validateAlphanumericOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'File number',
              hasAsterisk: true,
              hintText: 'Enter file number',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.fileNumberController,
              validateAlphanumericOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              hasAsterisk: false,
              dateText: 'Visa issue date',
              hintText: 'Select date',
              controller: widget.visaIssueDateController,
              onDateSelected: (date) {
                setState(() {
                  visaIssueDate = date;
                });
              },
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),

            CustomTextFieldWithHeading(
              Heading: 'Visa location',
              hintText: 'Enter visa location',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.visaLocationController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),

            CustomDatePickerField(
              dateText: 'Visa expiry date',
              hintText: 'Select date',
              controller: widget.visaExpiryDateController,
              onDateSelected: _validateVisaExpiryDate,
              isStartDate: false,
              startDate: visaIssueDate,
              selectFutureDates: true,
              selectPastDates: false,
              validator: _validateDate,
            ),
          ],

          if (widget.selectedDocumentType ==
              'Upload New Bank Account Details') ...[
            CustomTextFieldWithHeading(
              Heading: 'Account number',
              hasAsterisk: true,
              hintText: 'Enter account number',
              keyboardType: TextInputType.number,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.accountNumberController,
              validateNumbersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'IBAN number',
              hasAsterisk: true,
              hintText: 'Enter IBAN number',
              keyboardType: TextInputType.number,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.ibanNumberController,
              validateNumbersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Bank number',
              hasAsterisk: true,
              hintText: 'Enter bank number',
              keyboardType: TextInputType.number,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.bankNumberController,
              validateNumbersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Branch name',
              hasAsterisk: true,
              hintText: 'Enter branch name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.branchNameController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'BIS code',
              hintText: 'Enter BIS code',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.bisCodeController,
              validateAlphanumericOnly: true,
              maxLength: 50,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Passport') ...[
            CustomTextFieldWithHeading(
              Heading: 'Passport number',
              hasAsterisk: true,
              hintText: 'Enter passport number',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.passportNumberController,
              validateAlphanumericOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Issue date',
              hintText: 'Select date',
              controller: widget.visaIssueDateController,
              onDateSelected: (date) {
                setState(() {
                  passportIssueDate = date;
                });
              },
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload UAE Family Book') ...[
            CustomTextFieldWithHeading(
              Heading: 'Number',
              hintText: 'Enter Number',
              keyboardType: TextInputType.number,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller:
                  widget.visaNumberController ?? TextEditingController(),
              validateNumbersOnly: true,
              maxLength: 50,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Passport' ||
              widget.selectedDocumentType == 'Upload UAE Family Book') ...[
            CustomTextFieldWithHeading(
              Heading: 'Issue location',
              hintText: 'Enter issue location',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.visaLocationController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Issuing authority',
              hintText: 'Enter issuing authority',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.issuingAuthorityController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Passport' ||
              widget.selectedDocumentType == 'Upload Emirates ID') ...[
            const SizedBox(height: 24),

            CustomDatePickerField(
              dateText: 'Expiry date',
              hintText: 'Select date',
              controller: widget.certificateToDateController,
              onDateSelected:
                  (widget.selectedDocumentType == 'Upload Emirates ID' ||
                          widget.selectedDocumentType == 'Upload Passport')
                      ? (date) {
                        DateTime? issueDate;

                        if (widget.selectedDocumentType ==
                            'Upload Emirates ID') {
                          issueDate = emiratesIdIssueDate;
                        } else if (widget.selectedDocumentType ==
                            'Upload Passport') {
                          issueDate = passportIssueDate;
                        }

                        if (issueDate != null && date.isBefore(issueDate)) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Expiry date cannot be earlier than issue date',
                              ),
                              backgroundColor: AppColors.red,
                            ),
                          );
                          widget.certificateToDateController.clear();
                          return;
                        }
                        setState(() {
                          certificateToDate = date;
                        });
                      }
                      : _validateCertificateToDate,
              isStartDate:
                  (widget.selectedDocumentType == 'Upload Emirates ID' ||
                          widget.selectedDocumentType == 'Upload Passport')
                      ? false
                      : true,
              startDate:
                  widget.selectedDocumentType == 'Upload Emirates ID'
                      ? emiratesIdIssueDate
                      : widget.selectedDocumentType == 'Upload Passport'
                      ? passportIssueDate
                      : null,
              selectFutureDates: true,
              selectPastDates: false,
              validator: _validateDate,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload UAE Family Book') ...[
            const SizedBox(height: 24),
            CustomTextFieldWithHeading(
              showSpacing: false,
              Heading: 'Issuing comments',
              hintText: 'Add comments here',
              hasAsterisk: false,
              fontSize: 14,
              fontFamily: 'Open Sans',
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              controller: widget.issuingCommentsController,
              maxLength: 500,
            ),
          ],

          if (widget.selectedDocumentType ==
                  'Upload Attested University Certificates' ||
              widget.selectedDocumentType == 'Upload Certificate Equivalency' ||
              widget.selectedDocumentType == 'Upload Experience Letter' ||
              widget.selectedDocumentType ==
                  'Upload Police Clearance Certificate' ||
              widget.selectedDocumentType ==
                  'Upload Spouse\'s Detailed Salary Certificate' ||
              widget.selectedDocumentType == 'Upload University Certificate' ||
              widget.selectedDocumentType == 'Upload Work Medical Fitness') ...[
            CustomTextFieldWithHeading(
              Heading: 'Name',
              hasAsterisk:
                  widget.selectedDocumentType !=
                      'Attached university certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType !=
                      'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',
              hintText: 'Enter name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.certificateNameController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Number',
              hasAsterisk:
                  widget.selectedDocumentType !=
                      'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType !=
                      'Upload Attested University Certificates' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType !=
                      'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',
              hintText: 'Enter number',
              keyboardType: TextInputType.number,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.Numbercontroller,
              validateNumbersOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              hasAsterisk:
                  widget.selectedDocumentType !=
                      'Upload Attested University Certificates' &&
                  widget.selectedDocumentType !=
                      'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType !=
                      'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',
              dateText: 'From date',
              hintText: 'Select date',
              controller: widget.certificateFromDateController,
              onDateSelected: (date) {
                setState(() {
                  certificateFromDate = date;
                });
              },
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              hasAsterisk:
                  widget.selectedDocumentType !=
                      'Upload Attested University Certificates' &&
                  widget.selectedDocumentType !=
                      'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType !=
                      'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',
              dateText: 'To date',
              hintText: 'Select date',
              controller: widget.certificateToDateController,
              onDateSelected: _validateCertificateToDate,
              isStartDate: false,
              startDate: certificateFromDate,
              selectFutureDates: true,
              selectPastDates: false,
              validator: _validateDate,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              hasAsterisk:
                  widget.selectedDocumentType !=
                      'Upload Attested University Certificates' &&
                  widget.selectedDocumentType !=
                      'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType !=
                      'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType !=
                      'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',

              dateText: 'Issued on',
              hintText: 'Select date',
              controller: widget.certificateIssuedOnController,
              onDateSelected: (date) {},
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
            const SizedBox(height: 24),
      
 HeadingText(
              text: 'Issuing country',
              hasAsterisk: widget.selectedDocumentType != 'Upload Certificate Equivalency' &&
                  widget.selectedDocumentType != 'Upload Attested University Certificates' &&
                  widget.selectedDocumentType != 'Upload Experience Letter' &&
                  widget.selectedDocumentType != 'Upload Police Clearance Certificate' &&
                  widget.selectedDocumentType != 'Upload Spouse\'s Detailed Salary Certificate' &&
                  widget.selectedDocumentType != 'Upload University Certificate' &&
                  widget.selectedDocumentType != 'Upload Work Medical Fitness',
            ),
            const SizedBox(height: 8),
            DocCountryDropDown(
              initialValue: widget.countryController.text.isEmpty ? null : widget.countryController.text,
              onChanged: (value) {
                widget.countryController.text = value ?? '';
                setState(() {}); // Trigger rebuild to update displayed value
              },
              countryController: widget.countryController,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Issuing location',
              hintText: 'Enter issuing location',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.certificateIssuingLocationController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Issuing authority',
              hintText: 'Enter issuing authority',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Open Sans',
              hintStyle: AppColors.lightGreyshade,
              controller: widget.certificateIssuingAuthorityController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            CustomTextFieldWithHeading(
              showSpacing: false,
              Heading: 'Issuing comments',
              hintText: 'Add comments here',
              hasAsterisk: false,
              fontSize: 14,
              fontFamily: 'Open Sans',
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              controller: widget.certificateIssuingCommentsController,
              maxLength: 500,
            ),
          ],

          const SizedBox(height: 24),
          if (widget.selectedDocumentType == 'Upload Vaccine Status') ...[
            CustomDropdown(
              label: "Vaccine status",
              hasAsterisk: true,
              hintText: "Select status",
              options: const ['Fully vaccinated', 'Not vaccinated'],
              selectedValue: widget.selectedVaccineStatus,
              onChanged: widget.onOptionChanged,
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Latest vaccine date',
              hintText: 'Select date',
              controller: widget.vaccineDateController,
              onDateSelected: _validateFutureDate,
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
          ],

          if (widget.selectedDocumentType == 'Upload Birth Certificate') ...[
            CustomDatePickerField(
              dateText: 'Date of birth',
              hintText: 'Select date',
              controller: widget.dateOfBirthController,
              onDateSelected: _validateFutureDate,
              selectPastDates: true,
              selectFutureDates: false,
              validator: _validateDate,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Place of birth',
              hasAsterisk: true,
              hintText: 'Enter place of birth',
              controller: widget.placeOfBirthController,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            HeadingText(text: "Person's nationality", hasAsterisk: true),
            const SizedBox(height: 8),
            CountryDropdownOverlay(countryProvider: countryProvider),
            CustomTextFieldWithHeading(
              Heading: 'Name of mother',
              hasAsterisk: true,

              hintText: 'Enter name of mother',
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              controller: widget.nameOfMotherController,
              validateLettersOnly: true,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            HeadingText(text: "Mother's nationality", hasAsterisk: true),
            const SizedBox(height: 8),
            CountryDropdownOverlay(countryProvider: motherCountryProvider),
            // const SizedBox(height: 24),
            CustomTextFieldWithHeading(
              hintStyle: AppColors.lightGreyshade,
              hasAsterisk: true,
              hintColor: AppColors.lightGreyshade,
              fillColor: AppColors.whiteColor,
              hintText: 'Enter name of father',
              controller: widget.nameOfFatherController,
              validateLettersOnly: true,
              maxLength: 50,
              Heading: 'Name of father',
            ),
            const SizedBox(height: 24),
            HeadingText(text: "Father's nationality", hasAsterisk: true),
            const SizedBox(height: 8),
            CountryDropdownOverlay(countryProvider: fatherCountryProvider),
          ],
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/screens/account/profile/components/personInfoWidget.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';
import 'package:seawork/screens/account/profile/personalInfo/components/userPermission.dart';
import 'package:seawork/screens/account/profile/repository/LegalRepository.dart';
import 'package:seawork/screens/account/profile/repository/assignmentRepository.dart';
import 'package:seawork/screens/account/profile/repository/gradeRepository.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/utils/style/colors.dart';

// Add this provider for legal employers data
final legalEmployersProvider = FutureProvider<List<dynamic>?>((ref) async {
  final repo = ref.read(legalLovRepositoryProvider);
  return await repo.fetchLegalEmployers();
});

final gradeLovFutureProvider = FutureProvider<List<dynamic>?>((ref) async {
  final repo = ref.read(gradeLovRepositoryProvider);
  return await repo.fetchGradeDetails();
});

class EmployeeInfo extends ConsumerStatefulWidget {
  @override
  _EmploymentInfoScreenState createState() => _EmploymentInfoScreenState();
}

class _EmploymentInfoScreenState extends ConsumerState<EmployeeInfo> {
  String? selectedFieldGlobal;
  String? selectedFieldLocal;
  String? selectedFieldDemographic;
  EmployeeDetailModel? employeeDetails;
  Assignment? assignment;
  Map<String, dynamic>? assignmentppf;
  Map<String, dynamic>? gradeDetails;
  bool _isLoading = true;
  bool _hasPermission = true;

  @override
  void initState() {
    super.initState();
    _checkPermissionsAndFetchData();
  }

  Future<void> _checkPermissionsAndFetchData() async {
    try {
      final userProfile = await ref.read(userProfileProvider.future);
      final userPermission = UserPermission(userProfile);

      if (!userPermission.hasEMSAccess && mounted) {
        if (mounted) {
          setState(() {
            _hasPermission = false;
            _isLoading = false;
          });
        }
        return;
      }

      await Future.wait([
        _fetchEmployeeDetails(),
        _fetchAssignmentDetails(),
        _fetchGradeDetails(),
      ]);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error initializing data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchEmployeeDetails() async {
    try {
      final profileRepository = ref.read(profileRepositoryProvider);
      final details = await profileRepository.getProfileDetails();

      if (mounted && (details?.items?.isNotEmpty ?? false)) {
        setState(() {
          employeeDetails = details;
          assignment =
              (details?.items?.first.assignments?.isNotEmpty ?? false)
                  ? details?.items!.first.assignments!.first
                  : null;
        });

        // if (assignment?.gradeId != null) {
        //  fetchGradeDetails(assignment!.gradeId!);
        // }
      }
    } catch (e) {
      debugPrint('Error fetching employee details: $e');
      if (mounted) {
        setState(() {
          employeeDetails = null;
          assignment = null;
        });
      }
    }
  }

  Future<void> _fetchAssignmentDetails() async {
    try {
      final details =
          await ref
              .read(assignmentPPfRepositoryProvider)
              .getChildAssignmentPpf();
      if (mounted && details.isNotEmpty) {
        setState(() {
          assignmentppf = details[0];
        });
      }
    } catch (e) {
      debugPrint('Error fetching assignment details: $e');
      if (mounted) {
        setState(() {
          assignmentppf = null;
        });
      }
    }
  }

  Future<void> _fetchGradeDetails() async {
    final gradeData =
        await ref.read(gradeLovRepositoryProvider).fetchGradeDetails();
    if (mounted) {
      setState(() {
        gradeDetails =
            (gradeData != null && gradeData.isNotEmpty)
                ? gradeData.first
                : null;
      });
    }
  }

  void _onFieldTapped(String label) {
    setState(() {
      selectedFieldDemographic = label;
    });
  }

  void _onItemTapped(int index) {
    setState(() {});
  }

  Widget _buildGradeInfo() {
    if (gradeDetails == null) {
      return SelectableRow(
        label: "Grade",
        value: "",
        isSelected: selectedFieldDemographic == 'Grade',
        onTap: _onFieldTapped,
      );
    }

    return SelectableRow(
      label: "Grade",
      value:
          gradeDetails?['Name']?.toString() ??
          gradeDetails?['gradeName']?.toString() ??
          "---",
      isSelected: selectedFieldDemographic == 'Grade',
      onTap: _onFieldTapped,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasPermission) {
      return const SizedBox.shrink();
    }

    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          title: "Employment info",
          showActionIcon: true,
          onBackPressed: () => context.pop(),
        ),
        body: const Center(
          child: CircularProgressIndicator(color: AppColors.viewColor),
        ),
      );
    }

            final legalEmployersAsyncValue = ref.watch(legalEmployersProvider);
            final item = employeeDetails?.items?.first;

   return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: "Employment info",
        showActionIcon: true,
        onBackPressed: () => context.pop(),
      ),
      body: Padding20x0(
         ListView(
          children: [
            Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadow,
                    blurRadius: 4.6,
                    spreadRadius: 0,
                    offset: const Offset(0, 0),
               ) ],
              ),
              child: CardSectionTwo(
                shadowColor: AppColors.shadowBoxColor.withOpacity(0.25),
                title: "Contract info",
                subtitle: "",
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 20, right: 20, bottom: 12),
                    child: Divider(
                      color: AppColors.lightBlueBackgroundColor,
                      thickness: 1,
                    ),
                  ),
                  SelectableRow(
                    label: "Contract Number",
                    value: item?.workMobilePhoneNumber?.toString() ?? "---",
                    isSelected: selectedFieldDemographic == "Contract Number",
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Type",
                    value: assignment?.fullPartTime ?? "---",
                    isSelected: selectedFieldDemographic == "Type",
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Start date",
                    value: item?.effectiveStartDate != null
                        ? DateFormat('dd MMM yyyy').format(item!.effectiveStartDate!)
                        : "---",
                    isSelected: selectedFieldDemographic == "Start date",
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Initial Duration",
                    value: assignment?.assignmentStatus ?? "---",
                    isSelected: selectedFieldDemographic == "Initial Duration",
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Contract end date",
                    value: assignment?.effectiveEndDate != null
                        ? DateFormat('dd- MMM- yyyy')
                            .format(assignment!.effectiveEndDate!)
                        : "---",
                    isSelected: selectedFieldDemographic == "Contract end date",
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Employeement status",
                    value: assignment?.assignmentStatus ?? "N/A",
                    isSelected: selectedFieldDemographic == "Employeement status",
                    onTap: _onFieldTapped,
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadow,
                    blurRadius: 4.6,
                    spreadRadius: 0,
                    offset: const Offset(0, 0),
               ) ],
              ),
              child: CardSectionTwo(
                shadowColor: AppColors.shadowBoxColor.withOpacity(0.25),
                title: "Managers",
                subtitle: "",
                children: [],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadow,
                    blurRadius: 4.6,
                    spreadRadius: 0,
                    offset: const Offset(0, 0),
                 )   ],
              ),
              child: CardSectionTwo(
                shadowColor: AppColors.shadowBoxColor.withOpacity(0.25),
                title: "Employment history",
                subtitle: "",
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 20, right: 20, bottom: 12),
                    child: Divider(
                      color: AppColors.lightBlueBackgroundColor,
                      thickness: 1,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 20, bottom: 12),
                    child: HeadingText(text: "Hire"),
                  ),
                  SelectableRow(
                    label: "Hire to fill vacant position",
                    value: item?.hireDate != null
                        ? DateFormat('dd- MMM- yyyy').format(item!.hireDate!)
                        : "N/A",
                    isSelected:
                        selectedFieldDemographic == "Hire to fill vacant position",
                    onTap: _onFieldTapped,
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadow,
                    blurRadius: 4.6,
                    spreadRadius: 0,
                    offset: const Offset(0, 0),
               ) ],
              ),
              child: CardSectionTwo(
                shadowColor: AppColors.shadowBoxColor.withOpacity(0.25),
                title: "Assignment",
                subtitle: "",
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 20, right: 20, bottom: 12),
                    child: Divider(
                      color: AppColors.lightBlueBackgroundColor,
                      thickness: 1,
                    ),
                  ),
                  SelectableRow(
                    label: "Legal Employer",
                    value: legalEmployersAsyncValue.when(
                      data: (legalEmployers) {
                        return legalEmployers != null && legalEmployers.isNotEmpty
                            ? legalEmployers.first['Name']?.toString() ?? "---"
                            : "---";
                      },
                      loading: () => "Loading...",
                      error: (error, stack) => "Error",
                    ),
                    isSelected: selectedFieldDemographic == 'Legal Employer',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Business Unit",
                    value: legalEmployersAsyncValue.when(
                      data: (legalEmployers) {
                        return legalEmployers != null && legalEmployers.isNotEmpty
                            ? legalEmployers.first['Name']?.toString() ?? "---"
                            : "---";
                      },
                      loading: () => "Loading...",
                      error: (error, stack) => "Error",
                    ),
                    isSelected: selectedFieldDemographic == 'Business Unit',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Job",
                    value: item!.roles?.isNotEmpty == true
                        ? item.roles?.first.roleName ?? "---"
                        : assignment?.assignmentName ?? "---",
                    isSelected: selectedFieldDemographic == 'Job',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Department",
                    value: "----",
                    isSelected: selectedFieldDemographic == 'Department',
                    onTap: _onFieldTapped,
                  ),
                  _buildGradeInfo(),
                  SelectableRow(
                    label: "Location",
                    value: legalEmployersAsyncValue.when(
                      data: (legalEmployers) {
                        return legalEmployers != null && legalEmployers.isNotEmpty
                            ? legalEmployers.first['Name']?.toString() ?? "---"
                            : "---";
                      },
                      loading: () => "Loading...",
                      error: (error, stack) => "Error",
                    ),
                    isSelected: selectedFieldDemographic == 'Location',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Position",
                    value: assignment?.assignmentName ?? "---",
                    isSelected: selectedFieldDemographic == 'Position',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Assignment Category",
                    value: assignment?.assignmentCategory ?? "---",
                    isSelected: selectedFieldDemographic == 'Assignment Category',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Notice Period",
                    value: "----",
                    isSelected: selectedFieldDemographic == 'Notice Period',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Probation Period",
                    value: assignment?.probationPeriodLength != null
                        ? "${assignment?.probationPeriodLength} month"
                        : "---",
                    isSelected: selectedFieldDemographic == 'Probation Period',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Probation End Date",
                    value: assignment?.probationPeriodEndDate != null
                        ? DateFormat('dd- MM- yyyy')
                            .format(assignment!.probationPeriodEndDate!)
                        : "---",
                    isSelected: selectedFieldDemographic == 'Probation For Other',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Person Number",
                    value: item.personNumber ?? "---",
                    isSelected: selectedFieldDemographic == 'Person Number',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "People Group",
                    value: assignment?.peopleGroup ?? "---",
                    isSelected: selectedFieldDemographic == 'People Group',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Work Location",
                    value: assignmentppf?['workLocation']?.toString() ?? "----",
                    isSelected: selectedFieldDemographic == 'Work Location',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Point of Origin",
                    value: assignmentppf?['pointOfOrigin']?.toString() ?? "----",
                    isSelected: selectedFieldDemographic == 'Point Of Origin',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Class of Travel",
                    value: assignmentppf?['classOfTravel']?.toString() ?? "----",
                    isSelected: selectedFieldDemographic == 'Class of Travel',
                    onTap: _onFieldTapped,
                  ),
                  SelectableRow(
                    label: "Additional Title- Signature",
                    value: "-----",
                    isSelected:
                        selectedFieldDemographic == 'Additional Title Signature',
                    onTap: _onFieldTapped,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        onTap: _onItemTapped,
      ),
   ));
  }
}
// }

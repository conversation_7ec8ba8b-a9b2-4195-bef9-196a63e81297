import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/countryDropDown.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_types_model.dart';
import 'package:seawork/screens/employee/letterRequest/noc.dart';
import 'package:seawork/utils/style/colors.dart';

class RequestFormContent extends StatefulWidget {
  final LetterRequestTypesModel letterRequestType;
  final List<FileModel> uploadedFiles;
  final Function(List<FileModel>) onFilesChanged;
  final double screenHeight;

  // controllers (you can pass them all or a model that contains them)
  final TextEditingController accountNumberController;
  final TextEditingController ibanNumberController;
  final TextEditingController bankNameController;
  final TextEditingController branchNameController;
  final TextEditingController swiftCodeController;
  final TextEditingController nameInEnglishController;
  final TextEditingController nameInArabicController;
  final TextEditingController titleInEnglishController;
  final TextEditingController titleInArabicController;
  final TextEditingController mobileNumberController;
  final TextEditingController directNumberController;
  final TextEditingController remarksInEnglishController;
  final TextEditingController remarksInArabicController;
  final TextEditingController addressedToController;
  final TextEditingController travelStartDate;
  final TextEditingController travelEndDate;
  // final TextEditingController salaryCertificateDate;
  final TextEditingController link;

  const RequestFormContent({
    super.key,
    required this.letterRequestType,
    required this.uploadedFiles,
    required this.onFilesChanged,
    required this.screenHeight,
    required this.accountNumberController,
    required this.ibanNumberController,
    required this.bankNameController,
    required this.branchNameController,
    required this.swiftCodeController,
    required this.nameInEnglishController,
    required this.nameInArabicController,
    required this.titleInEnglishController,
    required this.titleInArabicController,
    required this.mobileNumberController,
    required this.directNumberController,
    required this.remarksInEnglishController,
    required this.remarksInArabicController,
    required this.addressedToController,
    required this.travelStartDate,
    required this.travelEndDate,
    // required this.salaryCertificateDate,
    required this.link,
  });

  @override
  _RequestFormContentState createState() => _RequestFormContentState();
}

class _RequestFormContentState extends State<RequestFormContent> {
  // State variables to track selected dates
  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  DateTime? selectedSalaryCertificateDate;

  @override
  Widget build(BuildContext context) {
    // 🔁 Move your entire conditional block here
    final documentType = widget.letterRequestType.documentType!;
    switch (documentType) {
      case "Request Bank Change":
        return Column(
          children: [
            CustomTextFieldWithHeading(
              Heading: 'Account number',
              hasAsterisk: true,
              hintText: 'eg: ****************',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.accountNumberController,
            ),
            CustomTextFieldWithHeading(
              Heading: 'IBAN number',
              hasAsterisk: true,
              hintText: 'eg: AE07033****************',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.ibanNumberController,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Bank name',
              hasAsterisk: true,
              hintText: 'Enter bank name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.bankNameController,
              restrictSpecialChars: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Branch name',
              hasAsterisk: true,
              hintText: 'Enter branch name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.branchNameController,
              restrictSpecialChars: true,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Swift code',
              hintText: 'eg: SHBJUAEAXXX',
              hasAsterisk: false,
              hintStyle: AppColors.lightGreyColor,
              maxlines: 1,
              fontSize: 14,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              controller: widget.swiftCodeController,
              validateSwiftCode: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 24),
            const HeadingText(text: 'Attachments', hasAsterisk: true),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
              showMinTwoAttachmentsMessage: true,
            ),

            const SizedBox(height: 24),
            const HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
            const SizedBox(height: 8),
            OpenSansText(
              'Upload the no-due certificate and new bank account details via attachments to submit the request.',
              color: AppColors.blackColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              fontheight: 1.5,
            ),
            const SizedBox(height: 34),
          ],
        );

      case "Request Business Card":
        return Column(
          children: [
            CustomTextFieldWithHeading(
              Heading: 'Name in English',
              hasAsterisk: true,
              hintText: 'Enter your name',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.nameInEnglishController,
              validateEnglishOnly: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Name in Arabic',
              hasAsterisk: true,
              hintText: 'أدخل اسمك',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.nameInArabicController,
              validateArabicOnly: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Title in English',
              hasAsterisk: true,
              hintText: 'Enter title',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.titleInEnglishController,
              validateEnglishOnly: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Title in Arabic',
              hasAsterisk: true,
              hintText: 'أدخل المسمى',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.titleInArabicController,
              validateArabicOnly: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Mobile number',
              hasAsterisk: true,
              hintText: 'eg: +971-XX-XXXXXXX',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.mobileNumberController,
              validateMobileNumber: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Direct number',
              hintText: 'eg: +971-XX-XXXXXXX',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.directNumberController,
              validateMobileNumber: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Remarks in English',
              hintText: 'Enter remark',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.remarksInEnglishController,
              validateEnglishWithNumbersAndSymbols: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            CustomTextFieldWithHeading(
              Heading: 'Remarks in Arabic',
              hintText: 'أدخل الملاحظات',
              keyboardType: TextInputType.text,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              fontSize: 14,
              fontFamily: 'Roboto',
              hintStyle: AppColors.lightGreyColor,
              controller: widget.remarksInArabicController,
              validateArabicWithNumbersAndSymbols: true,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              maxLength: 50,
            ),
            const SizedBox(height: 24),
            const HeadingText(text: 'Attachments'),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
            ),
            const SizedBox(height: 24),
            const HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
            const SizedBox(height: 34),
          ],
        );

      case "Request Embassy Letter":
        return Column(
          children: [
            SizedBox(height: widget.screenHeight * 0.03),
            HeadingText(text: 'Travelling country', hasAsterisk: true),
            SizedBox(height: widget.screenHeight * 0.01),
            Container(
              child: CountryDropdownOverlay(countryProvider: countryProvider),
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Travel start date',
              hintText: 'Select travel date',
              controller: widget.travelStartDate,
              onDateSelected: (date) {
                setState(() {
                  selectedStartDate = date;
                  // Clear end date if it's before the new start date
                  // if (selectedEndDate != null && selectedEndDate!.isBefore(date)) {
                  //   selectedEndDate = null;
                  //   widget.travelEndDate.clear();
                  // }
                });
              },
              isStartDate: true,
              // allowPastDates: false, // Allow any date for start date
            ),
            const SizedBox(height: 24),
            CustomDatePickerField(
              dateText: 'Travel end date',
              hintText: 'Select travel date',
              controller: widget.travelEndDate,
              onDateSelected: (date) {
                setState(() {
                  selectedEndDate = date;
                });
              },
              isStartDate: false,
              startDate: selectedStartDate, // Pass start date for validation
              // allowPastDates:
              //     false, // Allow any date, but will be restricted by start date
            ),
            const SizedBox(height: 24),
            HeadingText(text: 'Attachments'),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
            ),
            const SizedBox(height: 24),
            HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
            const SizedBox(height: 34),
          ],
        );

      case "Request Salary Certificate":
        return Column(
          children: [
            CustomTextFieldWithHeading(
              Heading: 'Addressed to',
              hintText: 'Enter',
              hasAsterisk: true,
              hintStyle: AppColors.lightGreyshade,
              maxlines: 1,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.meetbottomsheethinttextcolor,
              controller: widget.addressedToController,
              maxLength: 150,

              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            // CustomDatePickerField(
            //   dateText: 'Date',
            //   hintText: 'dd/mm/yyyy',
            //   controller: widget.salaryCertificateDate,
            //   onDateSelected: (date) {
            //     setState(() {
            //       selectedSalaryCertificateDate = date;
            //     });
            //   },
            //   isStartDate: true,
            //   allowPastDates: true, // Allow any date for salary certificate
            // ),
            const SizedBox(height: 24),
            const HeadingText(text: 'Attachments'),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
            ),
            const SizedBox(height: 24),
            HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
          ],
        );

      case "Request NOC for Drivers License":
        return Column(
          children: [
            SizedBox(height: widget.screenHeight * 0.03),
            HeadingText(text: 'Attachments'),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
            ),
            SizedBox(height: widget.screenHeight * 0.03),
            HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
          ],
        );

      case "Request Medical Fitness Letter":
      case "Request Residency Letter":
      case "Request Salary Transfer":
        return Column(
          children: [
            const SizedBox(height: 24),
            const HeadingText(text: 'Attachments'),
            SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: widget.uploadedFiles,
              onFilesChanged: widget.onFilesChanged,
            ),
            const SizedBox(height: 24),
            const HeadingText(text: 'Link'),
            const SizedBox(height: 8),
            LinkInputList(),
          ],
        );

      default:
        return const SizedBox.shrink(); // For unsupported document types
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';
import 'package:seawork/screens/account/profile/personalInfo/components/profileDetail.dart';
import 'package:seawork/screens/account/profile/personalInfo/components/userPermission.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/utils/style/colors.dart';

class Personalinfo extends ConsumerStatefulWidget {
  const Personalinfo({Key? key}) : super(key: key);

  @override
  ConsumerState<Personalinfo> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends ConsumerState<Personalinfo> {
  String? selectedFieldDemographic;
  EmployeeDetailModel? employeeDetails;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissionsAndFetchData();
  }

  Future<void> _checkPermissionsAndFetchData() async {
    final userProfile = await ref.read(userProfileProvider.future);
    final userPermission = UserPermission(userProfile);

    if (!userPermission.hasEMSAccess && mounted) {
      if (context.mounted) context.pop();
      return;
    }

    _fetchEmployeeDetails();
  }

  Future<void> _fetchEmployeeDetails() async {
    setState(() => isLoading = true);
    final profileRepository = ref.read(profileRepositoryProvider);
    final details = await profileRepository.getProfileDetails();

    if (mounted) {
      setState(() {
        employeeDetails = details;
        isLoading = false;
      });
    }
  }

  void _onFieldTapped(String label) {
    setState(() {
      selectedFieldDemographic = label;
    });
  }

  void _onItemTapped(int index) {
    setState(() {});
  }

  Item? get _firstItem {
    return employeeDetails?.items?.isNotEmpty == true
        ? employeeDetails?.items?.first
        : null;
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(userProfileProvider).when(
          loading: () => const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              ),
          error: (error, stack) =>
              Scaffold(body: Center(child: Text('Error: $error'))),
          data: (userProfile) {
            final userPermission = UserPermission(userProfile);

            if (!userPermission.hasEMSAccess) {
              return const SizedBox.shrink();
            }

            return PopScope(
              canPop: true,
              onPopInvoked: (didPop) {
                if (!didPop) {
                  context.pop();
                }
              },
              child: Scaffold(
                backgroundColor: AppColors.secondaryColor,
                appBar: CustomAppBar(
                  title: "Personal info",
                  onBackPressed: () {
                    context.pop();
                  },
                ),
                body: SafeArea(
                  child: PersonalInfoDetail(
                    firstItem: _firstItem,
                    selectedFieldDemographic: selectedFieldDemographic,
                    onFieldTapped: _onFieldTapped,
                    isLoading: isLoading,
                  ),
                ),
                bottomNavigationBar: CustomBottomNavigationBar(
                  onTap: _onItemTapped,
                ),
              ),
            );
          },
        );
  }
}
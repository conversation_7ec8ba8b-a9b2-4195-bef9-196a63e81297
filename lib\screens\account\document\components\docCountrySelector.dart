import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/absence/models/countryModel.dart';
import 'package:seawork/utils/style/colors.dart';

class DocCountryDropDown extends StatefulWidget {
  final String? initialValue;
  final ValueChanged<String?>? onChanged;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextEditingController countryController;

  const DocCountryDropDown({
    super.key,
    this.initialValue,
    this.onChanged,
    this.controller,
    this.focusNode,
    required this.countryController,
  });

  @override
  State<DocCountryDropDown> createState() => _DocCountryDropDownState();
}

class _DocCountryDropDownState extends State<DocCountryDropDown> {
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  final GlobalKey _dropdownKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;
  String? _selectedCountry;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedCountry = widget.initialValue;
    _searchController = widget.controller ?? TextEditingController();
    _searchFocusNode = widget.focusNode ?? FocusNode();
    
    // Initialize with controller value if it exists
    if (widget.countryController.text.isNotEmpty) {
      _selectedCountry = widget.countryController.text;
    }
    
    // Listen to external changes to the countryController
    widget.countryController.addListener(_handleControllerChange);
    
    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus && _overlayEntry == null) {
        _searchController.clear();
        setState(() {
          _searchQuery = '';
        });
      }
    });
  }

  void _handleControllerChange() {
    // Sync dropdown state with external controller changes
    if (widget.countryController.text.isEmpty && _selectedCountry != null) {
      setState(() {
        _selectedCountry = null;
      });
    } else if (widget.countryController.text.isNotEmpty && 
               widget.countryController.text != _selectedCountry) {
      setState(() {
        _selectedCountry = widget.countryController.text;
      });
    }
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
  }

  void _showOverlay() {
    _removeOverlay();

    final RenderBox? renderBox = _dropdownKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final size = renderBox.size;
    final position = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _removeOverlay,
        behavior: HitTestBehavior.opaque,
        child: Stack(
          children: [
            Positioned(
              left: position.dx,
              top: position.dy + size.height + 2,
              width: size.width,
              child: GestureDetector(
                onTap: () {},
                child: Material(
                  elevation: 10,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    height: 293,
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 8,
                          ),
                          child: Container(
                            height: 37,
                            decoration: BoxDecoration(
                              color: AppColors.inputfillColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _searchController,
                                    focusNode: _searchFocusNode,
                                    onChanged: (value) {
                                      setState(() {
                                        _searchQuery = value;
                                      });
                                    },
                                    textAlignVertical: TextAlignVertical.center,
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Search nationality',
                                      hintStyle: GoogleFonts.openSans(
                                        fontSize: 14,
                                        color: AppColors.lightGreyColor,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      isCollapsed: true,
                                      contentPadding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                    ),
                                    style: const TextStyle(
                                      fontFamily: 'Open Sans',
                                      fontSize: 14,
                                      color: AppColors.blackColor,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.search,
                                  color: AppColors.viewColor,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            itemCount: CountryData.countries
                                .where((country) => country
                                    .toLowerCase()
                                    .contains(_searchQuery.toLowerCase()))
                                .length,
                            itemBuilder: (context, index) {
                              final country = CountryData.countries
                                  .where((country) => country
                                      .toLowerCase()
                                      .contains(_searchQuery.toLowerCase()))
                                  .toList()[index];
                              final isSelected = country == _selectedCountry;

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                child: Material(
                                  color: isSelected
                                      ? AppColors.lightGreyColor2
                                      : AppColors.transparentColor,
                                  borderRadius: BorderRadius.circular(4),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(4),
                                    splashColor: AppColors.microinteraction,
                                    highlightColor: AppColors.microinteraction,
                                    onTap: () async {
                                      await Future.delayed(
                                        const Duration(milliseconds: 100),
                                      );
                                      setState(() {
                                        _selectedCountry = country;
                                      });
                                      widget.onChanged?.call(country);
                                      widget.countryController.text = country;
                                      _removeOverlay();
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      child: Center(
                                        child: OpenSansText(
                                          country,
                                          color: AppColors.color,
                                          fontSize: 14,
                                          fontWeight: isSelected
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    final overlay = Overlay.of(context);
    if (mounted) {
      overlay.insert(_overlayEntry!);
      setState(() {
        _isDropdownOpen = true;
      });

      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _searchFocusNode.requestFocus();
        }
      });
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null && mounted) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      if (mounted) {
        setState(() {
          _isDropdownOpen = false;
          _searchController.clear();
          _searchQuery = '';
          _searchFocusNode.unfocus();
        });
      }
    }
  }

  @override
  void dispose() {
    widget.countryController.removeListener(_handleControllerChange);
    if (widget.controller == null) {
      _searchController.dispose();
    }
    if (widget.focusNode == null) {
      _searchFocusNode.dispose();
    }
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleDropdown,
      child: Container(
        key: _dropdownKey,
        height: 43,
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.lightGreyColor2, width: 1),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              OpenSansText(
                _selectedCountry ?? 'Select country',
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: _selectedCountry != null
                    ? AppColors.blackColor
                    : AppColors.lightGreyshade,
              ),
              GestureDetector(
                onTap: _toggleDropdown,
                child: Transform.rotate(
                  angle: _isDropdownOpen ? 3.14159 : 0,
                  child: CustomSvgImage(
                    imageName: "ArrowLeft",
                    color: AppColors.viewColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
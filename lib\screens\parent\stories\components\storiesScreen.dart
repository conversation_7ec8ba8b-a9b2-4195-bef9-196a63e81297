import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart' as provider_pkg;
import 'package:seawork/components/form/bottomSheet.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/components/widget/customOutlineButton.dart';

import 'package:seawork/screens/parent/stories/components/advancedSettings.dart';
import 'package:seawork/screens/parent/stories/components/audienceBottomSheet.dart';
import 'package:seawork/screens/parent/stories/components/childrenBottomSheet.dart';
import 'package:seawork/screens/parent/stories/components/imageSelector.dart';
import 'package:seawork/screens/parent/stories/components/tagBottomSheet.dart';
import '../provider/storiesProvider.dart';

import 'package:seawork/utils/style/colors.dart';

void main() => runApp(MaterialApp(home: StoriesScreen()));

class StoriesScreen extends StatefulWidget {
  final bool hideRecentsSection;

  const StoriesScreen({Key? key, this.hideRecentsSection = false})
      : super(key: key);

  @override
  State<StoriesScreen> createState() => _StoriesScreenState();
}

class _StoriesScreenState extends State<StoriesScreen> {
  String? selectedAudienceType;
  List<Map<String, String>> selectedChildren = [];
  List<Map<String, String>> selectedTags = [];
  Set<String> previouslySelected = {};
  final TextEditingController _captionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize provider data - using a dummy parentId for now
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
      provider.loadCreatePostMeta(); // Backend handles parent ID identification
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
         backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Stories'),
      
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
          ImageSelectorWidget(hideRecents: true),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.inputfillColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _captionController,
                decoration: InputDecoration(
                  hintText: 'Add a caption...',
                    hintStyle: TextStyle(
                    fontFamily: 'OpenSans',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: AppColors.meetbottomsheethinttextcolor,
                  ),
                  border: InputBorder.none,
                ),
                maxLines: 3,
                onChanged: (value) {
                  final provider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
                  provider.setPostBody(value);
                },
              ),
            ),
            SizedBox(height: 24),
            Align(
              alignment: Alignment.centerLeft,
              child: DMSans600Large(
                18,
                'More settings',AppColors.blackColor
           
              ),
            ),
            SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: AppColors.whiteColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
    _buildSettingItem(
        SvgImage24x24('assets/images/eye_icon.svg'),
        'Audience',
        () {
             showAudienceBottomSheet(context, (type, nurseryIds) {
            setState(() {
              selectedAudienceType = type;
            });
            final provider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
            provider.setSelectedNurseries(nurseryIds);
            });
        },
          trailingText: selectedAudienceType,
      ),
             _buildSettingItem(
                    SvgImage24x24('assets/images/children.svg'),
                    'Children',
                    () async {
                      final storiesProvider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
                      final result = await showModalBottomSheet<List<Map<String, dynamic>>>(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (bottomSheetContext) {
                          return provider_pkg.ChangeNotifierProvider<StoriesProvider>.value(
                            value: storiesProvider,
                            child: DraggableScrollableSheet(
                              initialChildSize: 0.6,
                              minChildSize: 0.4,
                              maxChildSize: 0.9,
                              expand: false,
                              builder: (context, scrollController) {
                                return Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.whiteColor,
                                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                  ),
                                  child: ChildrenBottomSheet(),
                                );
                              },
                            ),
                          );
                        },
                      );

                      if (result != null) {
                        setState(() {
                          selectedChildren = result;
                        });
                      }
                    },
                    trailingWidget: selectedChildren.isNotEmpty
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: selectedChildren
                                .map((child) => Padding(
                                      padding: const EdgeInsets.only(left: 4),
                                      child: CircleAvatar(
                                        radius: 12,
                                        backgroundImage: AssetImage(child['image']!),
                                      ),
                                    ))
                                .toList(),
                          )
                        : null,
                  ),

                  _buildSettingItem(SvgImage24x24('assets/images/peopleadd.svg'), 'Add tag',      () async {
                                      final storiesProvider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
                                      final result = await showModalBottomSheet<List<Map<String, dynamic>>>(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        builder: (bottomSheetContext) => provider_pkg.ChangeNotifierProvider<StoriesProvider>.value(
                                          value: storiesProvider,
                                          child: TagBottomSheet(),
                                        ),
                                      );
                                      if (result != null) {

                                        setState(() {
                                        previouslySelected = result.map((person) => person['image']!).toSet();
                                         selectedTags = result;
                                        });
                                      }
                                    },
                                  trailingWidget: selectedTags.isNotEmpty
                                        ? Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              ...selectedTags.take(3).map((tag) => Padding(
                                                    padding: const EdgeInsets.only(left: 4),
                                                    child: CircleAvatar(
                                                      radius: 12,
                                                      backgroundImage: AssetImage(tag['image']!), 
                                                    ),
                                                  )),
                                              if (selectedTags.length > 3)
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 4),
                                                  child: OpenSans600Large(12,
                                                    '+${selectedTags.length - 3}',AppColors.viewColor,
                                                  ),
                                                ),
                                            ],
                                          )
                                        : null,
                                  ),
                  _buildSettingItem(SvgImage24x24('assets/images/settings.svg'), 'Advance settings',  () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => AdvanceSettingsSheet(),
                    );
                  },),
                ],
              ),
            ),
            SizedBox(height: 32),
       
          ],
        ),
      ),
       bottomNavigationBar: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SubmitRequestButton(
              text: "Publish",
              onPressed: () async {
                final provider = provider_pkg.Provider.of<StoriesProvider>(context, listen: false);
                final success = await provider.publishPost(
                  parentId: 123, // Replace with actual parent ID
                  userType: 1,
                  academicYearId: 4, // Replace with actual academic year ID
                );
                if (success) {
                  context.pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Post published successfully!')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to publish post')),
                  );
                }
              },
            ),
            const SizedBox(height: 12),
            Customoutlinebutton(
              text: "Save as draft",
              onPressed: () {
            
              },
            ),
          ],
        ),
      ),
    );
  }
 

   Widget _buildSettingItem(
    Widget leadingIcon,
    String title,
    VoidCallback onTap, {
    String? trailingText,
      Widget? trailingWidget,
  }) {
    return ListTile(
      leading: leadingIcon,
      title: OpenSans600Large(14, title, AppColors.blackColor),
      trailing: trailingWidget ??
          (trailingText != null
              ? OpenSans400Large(12, trailingText, AppColors.blackColor)
              : null),
      onTap: onTap,
    );
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBar.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBarItem.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/account/profile/repository/profileImageProvidor.dart';
import 'package:seawork/screens/account/profile/repository/profileImageRepository.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

final bottomNavBarProvider = StateProvider<int>((ref) => 0);

class CustomBottomNavigationBar extends ConsumerStatefulWidget {
  final Function(int) onTap;

  const CustomBottomNavigationBar({Key? key, required this.onTap})
      : super(key: key);

  @override
  ConsumerState<CustomBottomNavigationBar> createState() =>
      _CustomBottomNavigationBarState();
}

class _CustomBottomNavigationBarState
    extends ConsumerState<CustomBottomNavigationBar> {
  Uint8List? profileImage;
  bool isImageLoading = false;
  int _lastSelectedIndex = 0;
  DateTime _lastTapTime = DateTime.now();
  DateTime? _currentBackPressTime;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  Future<void> _loadProfileImage() async {
    if (isImageLoading) return;
    setState(() => isImageLoading = true);
    try {
      final imageRepo = ref.read(imageRepositoryProvider);
      final imageBytes = await imageRepo.fetchImageBytes();
      if (mounted) {
        setState(() => profileImage = imageBytes);
      }
    } catch (e) {
      debugPrint('Error loading profile image: $e');
    } finally {
      if (mounted) {
        setState(() => isImageLoading = false);
      }
    }
  }

  Future<bool> _handleBackButton(BuildContext context) async {
    final int selectedIndex = ref.read(bottomNavBarProvider);
    final String currentRoute = GoRouterState.of(context).uri.path;

    if (selectedIndex == 3 && currentRoute == '/my-profile') {
      ref.read(bottomNavBarProvider.notifier).state = 0; // fix
      context.go('/dashboard');
      return false;
    }

  if (context.canPop()) {
    context.pop();
    return false;
  }

    if (currentRoute != '/dashboard') {
      ref.read(bottomNavBarProvider.notifier).state = 0; // fix
      context.go('/dashboard');
      return false;
    }

  // Double-tap to exit logic (only when on dashboard and can't pop)
  final now = DateTime.now();
  final bool shouldExit = _currentBackPressTime == null ||
      now.difference(_currentBackPressTime!) > const Duration(seconds: 2);

    if (shouldExit) {
      _currentBackPressTime = now;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
        ),
      );
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final int selectedIndex = ref.watch(bottomNavBarProvider);

    Duration calculateDuration(int fromIndex, int toIndex) {
      final distance = (toIndex - fromIndex).abs();
      return Duration(milliseconds: 250 + (distance * 50)); // Reduced transition speed
    }

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        final shouldExit = await _handleBackButton(context);
        if (shouldExit && mounted) {
          try {
            await SystemNavigator.pop();
            await SystemChannels.platform
                .invokeMethod<void>('SystemNavigator.pop');
          } catch (e) {
            debugPrint('Error closing app: $e');
          }
        }
      },
      child: SafeArea(
        bottom: true,
        child: CurvedNavigationBar(
          backgroundColor: AppColors.transparentColor,
          color: AppColors.secondaryColor,
          items: [
            _buildHomeNavItem(isSelected: selectedIndex == 0),
            _buildCalendarNavItem(isSelected: selectedIndex == 1),
            _buildNavItem(
              iconName: 'ic_party-horn',
              label: 'Events',
              isSelected: selectedIndex == 2,
            ),
            _buildProfileNavItem(isSelected: selectedIndex == 3),
          ],
          index: selectedIndex,
          onTap: (index) {
            ref.read(bottomNavBarProvider.notifier).state = index;
            switch (index) {
              case 0:
                context.go('/dashboard');
                break;
              case 1:
                context.go('/calendar');
                break;
              case 2:
              case 3:
                context.go('/my-profile');
                break;
              default:
                context.go('/dashboard');
            }
          },
          height: 63,
          animationDuration:
              calculateDuration(_lastSelectedIndex, selectedIndex),
          animationCurve:
              _getAppropriateCurve(_lastSelectedIndex, selectedIndex),
        ),
      ),
    );
  }

  Curve _getAppropriateCurve(int fromIndex, int toIndex) {
    final distance = (toIndex - fromIndex).abs();
    if (distance == 1) return Curves.easeOutCubic;
    if (distance == 2) return toIndex > fromIndex ? Curves.fastOutSlowIn : Curves.easeOutBack;
    return toIndex > fromIndex ? Curves.elasticOut : Curves.easeOutCirc;
  }

  CurvedNavigationBarItem _buildNavItem({
    required String iconName,
    required String label,
    required bool isSelected,
  }) {
    return CurvedNavigationBarItem(
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        child: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          child: CustomSvgImage(
            imageName: iconName,
            color: isSelected
                ? AppColors.viewColor
                : AppColors.bottomNavColor,
          ),
        ),
      ),
      label: label,
      labelStyle: _labelStyle(isSelected),
    );
  }

  CurvedNavigationBarItem _buildHomeNavItem({
    required bool isSelected,
  }) {
    return CurvedNavigationBarItem(
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        child: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          child: SizedBox(
            width: 24,
            height: 24,
            child: CustomSvgImage(
              imageName: isSelected ? 'colorfulhome' : 'colorlesshome',
            ),
          ),
        ),
      ),
      label: 'Home',
      labelStyle: _labelStyle(isSelected),
    );
  }

  CurvedNavigationBarItem _buildCalendarNavItem({
    required bool isSelected,
  }) {
    return CurvedNavigationBarItem(
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        child: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          child: SizedBox(
            width: 24,
            height: 24,
            child: CustomSvgImage(
              imageName: isSelected ? 'newcalendr' : 'calen',
            ),
          ),
        ),
      ),
      label: 'Calendar',
      labelStyle: _labelStyle(isSelected),
    );
  }

  CurvedNavigationBarItem _buildProfileNavItem({
    required bool isSelected,
  }) {
    return CurvedNavigationBarItem(
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        child: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          child: ClipOval(
            child: SizedBox(
              width: 24,
              height: 24,
              child: ref.watch(imageBytesProvider).when(
                    data: (bytes) => Image.memory(
                      bytes,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          CustomPngImage(
                        imageName: 'noprofile',
                        height: 89,
                        width: 89,
                      ),
                    ),
                    loading: () => CustomPngImage(
                      imageName: 'noprofile',
                      height: 89,
                      width: 89,
                    ),
                    error: (e, _) => CustomPngImage(
                      imageName: 'noprofile',
                      height: 89,
                      width: 89,
                    ),
                  ),
            ),
          ),
        ),
      ),
      label: 'Profile',
      labelStyle: _labelStyle(isSelected),
    );
  }

  TextStyle _labelStyle(bool isSelected) {
    return GoogleFonts.openSans(
      fontSize: 10,
      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
      color: isSelected ? AppColors.viewColor : AppColors.bottomNavColor,
    );
  }
}

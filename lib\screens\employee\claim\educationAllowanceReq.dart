import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:seawork/screens/employee/claim/components/educationallowance.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customAppbar.dart';

class EducationAllowance extends StatelessWidget {
  final List<String> academicYearOptions = [
    '2023-2024',
    '2024-2025',
    '2025-2026',
  ];
  final List<String> childNameOptions = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];
  final double appBarHeight = kToolbarHeight; // Use standard toolbar height

  EducationAllowance({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true, // Allows the back button/swipe gesture to work
      onPopInvoked: (didPop) {
        if (!didPop) {
          context.pop(); // Handle the back navigation
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          title: 'Education allowance request',
          showActionIcon: true,
          onBackPressed: () {
            context.pop(); // Ensure back button also works
          },
        ),
        body: SafeArea(
          child: EducationAllowanceForm(
            academicYearOptions: academicYearOptions,
            childNameOptions: childNameOptions,
            hideAppBar: true,
            onSubmit: (
              academicYear,
              childNames,
              childAmountControllers,
              links,
              comments,
            ) {
              // Handle submission
            },
          ),
        ),
      ),
    );
  }
}
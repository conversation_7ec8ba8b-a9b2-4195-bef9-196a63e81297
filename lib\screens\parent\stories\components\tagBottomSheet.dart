import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/components/widget/customSearchBar.dart';
import 'package:seawork/utils/style/colors.dart';
import '../provider/storiesProvider.dart';

class TagBottomSheet extends StatefulWidget {
  final Set<String>? previouslySelected;
   const TagBottomSheet({Key? key, this.previouslySelected}) : super(key: key);

  @override
  State<TagBottomSheet> createState() => _TagBottomSheetState();
}

class _TagBottomSheetState extends State<TagBottomSheet> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final provider = Provider.of<StoriesProvider>(context, listen: false);
        provider.loadTagProfiles();
      } catch (e) {
        debugPrint('Error accessing StoriesProvider in TagBottomSheet: $e');
      }
    });
  } 



  final TextEditingController searchController = TextEditingController();


  void onSearch(String query) async {
    final provider = Provider.of<StoriesProvider>(context, listen: false);
    // This will trigger a new fetch with search query
    // For now, we'll just trigger a rebuild
    setState(() {});
  }
  @override
  Widget build(BuildContext context) {
    return Consumer<StoriesProvider>(
      builder: (context, provider, child) {
        final tagProfiles = provider.tagProfiles;

        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (_, controller) {
            return Container(
              decoration: const BoxDecoration(
                color: AppColors.whiteColor,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Center(
                    child: CustomSvgImage(
                      imageName: "Rectangle12231",
                      height: 5,
                      width: 30,
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgImage24x24("assets/images/peopleadd.svg"),
                          const SizedBox(width: 8),
                          OpenSans600Large(16, "Add tag", AppColors.blackColor),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Divider(color: AppColors.dividerColor1),
                  const SizedBox(height: 12),
                  CustomSearchField(
                    showSearchField: true,
                    searchController: searchController,
                    onSearch: () => onSearch(searchController.text),
                    toggleSearch: () {},
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: provider.loadingTagProfiles
                        ? Center(child: CircularProgressIndicator())
                        : tagProfiles.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.people, size: 48, color: Colors.grey),
                                    SizedBox(height: 16),
                                    OpenSans400Large(14, 'No users found to tag', Colors.grey),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                controller: controller,
                                itemCount: tagProfiles.length,
                                itemBuilder: (context, index) {
                                  final profile = tagProfiles[index];
                                  final isSelected = provider.selectedTagUserIds.contains(profile.id);

                                  return ListTile(
                                    contentPadding: EdgeInsets.zero,
                                    leading: CircleAvatar(
                                      backgroundColor: Colors.transparent,
                                      radius: 20,
                                      child: profile.profileKey?.isNotEmpty == true
                                          ? FutureBuilder<Uint8List?>(
                                              future: provider.getImageBytes(profile.profileKey!),
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData && snapshot.data != null) {
                                                  return ClipOval(
                                                    child: Image.memory(
                                                      snapshot.data!,
                                                      fit: BoxFit.cover,
                                                      width: 40,
                                                      height: 40,
                                                      errorBuilder: (context, error, stackTrace) {
                                                        return Image.asset('assets/images/fatherAvatar.png');
                                                      },
                                                    ),
                                                  );
                                                }
                                                return Image.asset('assets/images/fatherAvatar.png');
                                              },
                                            )
                                          : Image.asset('assets/images/fatherAvatar.png'),
                                    ),
                                    title: OpenSans600Large(
                                      14,
                                      profile.nameEn,
                                      AppColors.blackColor,
                                    ),
                                    subtitle: OpenSans400Large(
                                      14,
                                      profile.role ?? 'Student',
                                      AppColors.blackColor,
                                    ),
                                    trailing: isSelected
                                        ? SvgImage24x24("assets/images/greenTick.svg")
                                        : null,
                                    onTap: () {
                                      provider.toggleTag(profile.id);
                                    },
                                  );
                                },
                              ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: SubmitRequestButton(
                      text: "Done",
                      onPressed: () {
                        final provider = Provider.of<StoriesProvider>(context, listen: false);
                        final selectedTagProfiles = provider.tagProfiles.where((profile)=> provider.selectedTagUserIds.contains(profile.id)).map((profile)=>{
                          'id': profile.id,
                          'name': profile.nameEn,
                          'profileKey': profile.profileKey,
                        }).toList();
                        Navigator.of(context).pop(selectedTagProfiles);
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
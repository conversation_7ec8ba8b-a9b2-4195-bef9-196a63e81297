import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customDuraDropdown.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/employee/absence/components/customAbsenceBalncer.dart';
import 'package:seawork/components/widget/customSubmitConfirmationDialog.dart';

import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';

class ToilRequest extends ConsumerStatefulWidget {
  const ToilRequest({super.key});

  @override
  ConsumerState<ToilRequest> createState() => _ToilRequestState();
}

class _ToilRequestState extends ConsumerState<ToilRequest> {
  DateTime? selectedStartDate; // Initially null
  DateTime? selectedEndDate; // Initially null
  String _startDateSelectedOption = "Full day"; // Default value
  String _endDateSelectedOption = "Full day"; // Default value
  int balanceDays = 12;
  double projectedBalance = 0.0;
  bool _isCharacterLimitExceeded = false;

  // Calculate projected balance based on the selected options
  void _updateProjectedBalance() {
    setState(() {
      projectedBalance = _calculateProjectedBalance();
    });
  }

  double _calculateProjectedBalance() {
    double deduction = 0;
    if (_startDateSelectedOption == "Full day") {
      deduction += 1;
    } else {
      deduction += 0.5;
    }

    // Only add end date deduction if dates are different
    if (selectedEndDate != null &&
        selectedStartDate != null &&
        !_isSameDay(selectedStartDate!, selectedEndDate!)) {
      if (_endDateSelectedOption == "Full day") {
        deduction += 1;
      } else {
        deduction += 0.5;
      }
    }

    return balanceDays - deduction;
  }

  // Check if both start and end dates are selected
  bool _areDatesFilled() {
    return selectedStartDate != null && selectedEndDate != null;
  }

  // Check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    bool isSubmitEnabled = _areDatesFilled(); // Button state
    bool isSameDate =
        selectedStartDate != null &&
        selectedEndDate != null &&
        _isSameDay(selectedStartDate!, selectedEndDate!);
    final planBalancesAsync = ref.watch(getPlanBalanceProvider);
    print(planBalancesAsync);

    void submitLeaveRequest(WidgetRef ref) async {
      ref.read(isSubmittingProvider.notifier).state = true; // Start Loading
      String formattedStartDate = DateFormat(
        'yyyy-MM-dd',
      ).format(selectedStartDate!);
      String formattedEndDate = DateFormat(
        'yyyy-MM-dd',
      ).format(selectedEndDate!);
      String currentTime = DateFormat('HH:mm').format(DateTime.now());

      // 🔹 Loop through each uploaded file and create attachments

      // 🔹 Create the request payload
      Map<String, dynamic> annualLeaveData = {
        // "personNumber": masriPersonNumber,
        "employer": employer,
        "absenceType": 'Time off in Lieu',
        "startDate": formattedStartDate.toString(),
        "startTime": currentTime,
        "endDate": formattedEndDate.toString(),
        "endTime": currentTime,
        "startDateDuration": 1,
        "endDateDuration": 1,
        "absenceStatusCd": "SUBMITTED",
        "comments": _commentsController.text,
        // Ensure attachments are converted to JSON
      };

      print('Leave Type: ${'Time off in Lieu'}');
      print('-----------annualLeaveData------------${annualLeaveData}');

      AbsenceRequest annualLeaveRequest = AbsenceRequest.fromAbsenceType(
        'Time off in Lieu',
        annualLeaveData,
      );

      try {
        // 🔹 Step 1: Post Leave Request & Get Unique ID
        dynamic absenceRecord = await ref.read(
          leaveRequestProvider(annualLeaveRequest).future,
        );
        String absencesUniqID =
            absenceRecord['personAbsenceEntryId'].toString();
        print('absenceuniqid========$absencesUniqID');

        // 🔹 Step 2: Show Success Message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Leave Request Submitted Successfully")),
        );

        // 🔹 Step 3: Show Confirmation Dialog & Navigate to Dashboard
        await showDialog(
          context: context,
          builder: (context) {
            return CustomSubmitConfirmationDialogBox(
              date1: selectedStartDate!,
              type: 'Time off in Lieu',
              date2: selectedEndDate!,
              onClose: () {
                context.go('/dashboard');
              },
            );
          },
        );
      } catch (error) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("Error: $error")));
      } finally {
        ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      }
    }

    // Extract balance based on selected leave type
    double? selectedBalance;
    double? AbsenceBalance;
    List<PlanBalanceModel> previousPlanItems = [];
    void handlePlanBalances(
      AsyncValue<List<PlanBalanceModel>> planBalancesAsync,
      String leaveType,
      Function(double?) onBalanceUpdated,
    ) {
      planBalancesAsync.when(
        data: (planBalances) {
          // Ensure that planBalances is a List<PlanBalanceModel>
          if (planBalances.isEmpty) {
            onBalanceUpdated(-1);
            return;
          }

          // Check if any plan matches the leaveType
          final hasMatchingPlan = planBalances.any(
            (plan) => plan.planName == leaveType,
          );

          if (!hasMatchingPlan) {
            // If no matching plan found, set balance to -1
            onBalanceUpdated(-1);
            print("Absence Balance: -1 (No matching leave type)");
            return;
          }

          // Find the matching plan
          final matchingPlan = planBalances.firstWhere(
            (plan) => plan.planName == leaveType,
          );

          // Compute AbsenceBalance
          final selectedBalance =
              matchingPlan.balanceAsOfBalanceCalculationDate;
          final absenceBalance =
              selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

          print("Absence Balance: $absenceBalance");

          // Pass the balance back via callback function
          onBalanceUpdated(absenceBalance as double?);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) {
          print("Error: $err");
          onBalanceUpdated(-1);
        },
      );
    }

    handlePlanBalances(ref.watch(getPlanBalanceProvider), 'Time off in Lieu', (
      balance,
    ) {
      setState(() {
        AbsenceBalance = balance;
      });
    });
    void _navigateToDashboard() {
      context.go('/dashboard');
    }

    Future<bool> _onWillPop() async {
      _navigateToDashboard();
      return false; // Prevents default back behavior
    }

    return  PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: AppBar(
          backgroundColor: AppColors.secondaryColor,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const CustomSvgImage(imageName: 'appbackbutton'),
            onPressed: () => Navigator.pop(context),
          ),
          title: Center(
            child: DmSansText(
              'Time off in lieu request',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.viewColor,
            ),
          ),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: _navigateToDashboard,
                child: Row(
                  children: [
                    CustomSvgImage(imageName: 'Vector'),
                    SizedBox(width: 4),
                  ],
                ),
              ),
            ),
          ],
          elevation: 0,
        ),

        // Using resizeToAvoidBottomInset: false to prevent the screen from resizing
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Column(
            children: [
              // Main content in an Expanded widget with SingleChildScrollView
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 32),
                        _buildLabel("Absence type", isRequired: true),
                        const SizedBox(height: 8),
                        _buildDropdownField("Time off in Lieu"),
                        const SizedBox(height: 8),
                        AbsenceBalanceWidget(
                          absenceType: '',
                          balance: AbsenceBalance,
                        ),

                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.end,
                        //   children: [
                        //     RichText(
                        //       text: TextSpan(
                        //         text: "Absence type balance: ",
                        //         style: const TextStyle(
                        //           color: Color(0XFF000000),
                        //           fontWeight: FontWeight.w400,
                        //           fontFamily: 'Open Sans',
                        //           fontSize: 12
                        //         ),
                        //         children: [
                        //           TextSpan(
                        //             text: "$balanceDays",
                        //             style: const TextStyle(
                        //               fontWeight: FontWeight.w600, // Only apply semibold to the number
                        //             ),
                        //           ),
                        //           const TextSpan(
                        //             text: " days",
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        const SizedBox(height: 24),
                        _buildDateSection("Start date", true),
                        const SizedBox(height: 20),
                        _buildDateSection(
                          "End date",
                          false,
                          hideOption: isSameDate,
                        ),
                        const SizedBox(height: 8),
                        if (selectedStartDate != null &&
                            selectedEndDate != null)
                          DurationBalanceWidget(
                            onProjectedBalanceUpdated: (p0) {
                              projectedBalance = p0;
                            },
                            onDurationCalculated: (p0) {
                              var durationInDays = p0;
                            },
                            absenceBalance: AbsenceBalance,
                            startDate: selectedStartDate!,
                            endDate: selectedEndDate!,
                          ),

                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //   children: [
                        //     RichText(
                        //       text: TextSpan(
                        //         text: "Duration: ",
                        //         style: const TextStyle(
                        //             color: Color(0XFF3E98FF),
                        //             fontWeight: FontWeight.w400,
                        //             fontFamily: 'Open Sans',
                        //             fontSize: 12),
                        //         children: [
                        //           TextSpan(
                        //             text: "1",
                        //             style: const TextStyle(
                        //               fontWeight: FontWeight
                        //                   .w600, // Only apply semibold to the number
                        //             ),
                        //           ),
                        //           const TextSpan(
                        //             text: " day",
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //     RichText(
                        //       text: TextSpan(
                        //         text: "Projected balance: ",
                        //         style: const TextStyle(
                        //             color: Color(0XFF3E98FF),
                        //             fontWeight: FontWeight.w400,
                        //             fontFamily: 'Open Sans',
                        //             fontSize: 12),
                        //         children: [
                        //           TextSpan(
                        //             text: "${_calculateProjectedBalance()}",
                        //             style: const TextStyle(
                        //               fontWeight: FontWeight
                        //                   .w600, // Only apply semibold to the number
                        //             ),
                        //           ),
                        //           const TextSpan(
                        //             text: " days",
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        const SizedBox(height: 24),
                        _buildLabel("Comments"),
                        const SizedBox(height: 8),
                        _buildCommentBox(),
                        // Add extra padding at the bottom for scrolling beyond the button
                        const SizedBox(height: 100),
                      ],
                    ),
                  ),
                ),
              ),

              // Button at the bottom with 8px padding
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 32),
                child: _buildSubmitButton(
                  isSubmitEnabled,
                  submitLeaveRequest,
                  projectedBalance,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateSection(
    String label,
    bool isStartDate, {
    bool hideOption = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Date Picker Field
            Expanded(
              child: Container(
                width: double.infinity,
                child: CustomDatePickerField(
                  dateText: label,
                  hintText: "Select Date",
                  controller:
                      isStartDate ? _startDateController : _endDateController,
                  onDateSelected: (selectedDate) {
                    setState(() {
                      if (isStartDate) {
                        selectedStartDate = selectedDate;
                        // Ensure the end date is not before the start date
                        if (selectedEndDate == null ||
                            selectedEndDate!.isBefore(selectedDate)) {
                          selectedEndDate = selectedDate;
                          _endDateController.text = DateFormat(
                            'dd/MM/yyyy',
                          ).format(selectedDate);
                        }
                      } else {
                        if (selectedStartDate != null &&
                            selectedDate.isBefore(selectedStartDate!)) {
                          // Prevent selecting an end date before the start date
                          selectedEndDate = selectedStartDate;
                          _endDateController.text = DateFormat(
                            'dd/MM/yyyy',
                          ).format(selectedStartDate!);
                        } else {
                          selectedEndDate = selectedDate;
                        }
                      }
                      _updateProjectedBalance(); // Recalculate balance when date changes
                    });
                  },
                  isStartDate: isStartDate,
                  startDate: selectedStartDate,
                  endDate: selectedEndDate,
                  hasAsterisk: true,
                ),
              ),
            ),
            const SizedBox(width: 20),
            // Duration Dropdown (conditionally hidden)
            Expanded(
              child:
                  hideOption
                      ? const SizedBox() // Empty space to maintain layout
                      : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 22,
                          ), // Align with date picker field
                          DurationDropdown(
                            selectedOption:
                                isStartDate
                                    ? _startDateSelectedOption
                                    : _endDateSelectedOption,
                            onOptionSelected: (option) {
                              if (isStartDate) {
                                _startDateSelectedOption = option;
                              } else {
                                _endDateSelectedOption = option;
                              }
                              _updateProjectedBalance(); // Recalculate balance when option changes
                            },
                          ),
                        ],
                      ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLabel(String text, {bool isRequired = false}) {
    return RichText(
      text: TextSpan(
        text: text,
        style: GoogleFonts.dmSans(
          color: AppColors.blackColor,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        children: [
          if (isRequired)
            TextSpan(text: " *", style: TextStyle(color: AppColors.red)),
        ],
      ),
    );
  }

  Widget _buildDropdownField(String value) {
    return Container(
      height: 46,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.whiteColor, // Set background color to white
        border: Border.all(
          color: AppColors.lightGreyColor2, // Updated border color
          width: 1, // 1px solid border
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: DmSansText(
          value,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.blackColor,
        ),
      ),
    );
  }

  Widget _buildCommentBox() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 80, // Increased height for multiple paragraphs
          width: double.infinity,
          child: TextFormField(
            controller: _commentsController,
            maxLines: null, // Allows multiple lines
            minLines: 3, // Ensures a minimum of 3 paragraphs
            maxLength: 500,
            onChanged: (value) {
              setState(() {
                _isCharacterLimitExceeded = value.length > 500;
              });
            },
            decoration: InputDecoration(
              fillColor: AppColors.whiteColor,
              filled: true,
              contentPadding: EdgeInsets.symmetric(
                vertical: 20,
                horizontal: 12,
              ),
              hintText: "Enter",
              hintStyle: GoogleFonts.dmSans(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 19 / 14,
                color: AppColors.lightGreyshade,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.lightGreyColor2,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.lightGreyColor2,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.lightGreyColor2,
                  width: 1,
                ),
              ),
              counterText: '',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return "Please enter at least three paragraphs.";
              }
              List<String> paragraphs =
                  value
                      .trim()
                      .split("\n")
                      .where((p) => p.trim().isNotEmpty)
                      .toList();
              if (paragraphs.length < 3) {
                return "You must enter at least three paragraphs.";
              }

              return null;
            },
          ),
        ),
        if (_commentsController.text.length >= 500)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: OpenSans400Large(
              12,
              "Maximum 500 characters allowed",
              AppColors.redColor,
            ),
          ),
      ],
    );
  }

  Widget _buildSubmitButton(
    bool isSubmitEnabled,
    submitLeaveRequest,
    projectedBalance,
  ) {
    return ElevatedButton(
      onPressed:
          isSubmitEnabled && projectedBalance > 0
              ? () {
                submitLeaveRequest(ref);
              }
              : null,
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 56),
        backgroundColor:
            isSubmitEnabled ? AppColors.viewColor : AppColors.lightGreyshade,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 21, bottom: 18),
        child: Text(
          "Submit request",
          style: GoogleFonts.dmSans(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            height: 20.83 / 16,
            color: AppColors.whiteColor,
          ),
        ),
      ),
    );
  }
}

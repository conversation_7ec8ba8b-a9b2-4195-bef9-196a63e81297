import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/search/searchField.dart';
import 'package:seawork/screens/account/profile/components/tabSelector.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/screens/account/profile/components/birthdaySection.dart';
import 'package:seawork/screens/account/profile/components/calendarScreen.dart';
import 'package:seawork/screens/account/profile/components/directorySection.dart';
import 'package:seawork/screens/account/profile/provider/employeesNotifier.dart';
import 'package:seawork/screens/account/profile/provider/profileProvider.dart';
import 'package:seawork/utils/style/colors.dart';

class PeopleDirectory extends ConsumerStatefulWidget {
  const PeopleDirectory({super.key});

  @override
  ConsumerState<PeopleDirectory> createState() => _PeopleDirectoryState();
}

class _PeopleDirectoryState extends ConsumerState<PeopleDirectory> {
  int _selectedIndex = 0;
  int _selectedTab = 0;
  bool _showCalendar = false;
  bool _showSearchField = false;
  final ScrollController _scrollController = ScrollController();

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  DateTime? _selectedBirthdayDate;
  void _showCalendarDialog() {
    setState(() => _showCalendar = true);
  }

  Set<DateTime> _getBirthdayDates(List<Map<String, dynamic>> employees) {
    return employees
        .where((employee) {
          return employee['DateOfBirth'] != null;
        })
        .map((employee) {
          try {
            final dateOfBirth = employee['DateOfBirth'].toString();
            final date = DateTime.parse(dateOfBirth);
            return DateTime(DateTime.now().year, date.month, date.day);
          } catch (e) {
            return null;
          }
        })
        .where((date) => date != null)
        .cast<DateTime>()
        .toSet();
  }

  List<Map<String, dynamic>> _getFilteredEmployees(
    List<Map<String, dynamic>> employees,
  ) {
    if (_searchQuery.isEmpty) return employees;

    return employees.where((employee) {
      final displayName =
          employee['DisplayName']?.toString().toLowerCase() ?? '';
      final workEmail = employee['WorkEmail']?.toString().toLowerCase() ?? '';
      final city = employee['City']?.toString().toLowerCase() ?? '';
      final addressLine1 =
          employee['AddressLine1']?.toString().toLowerCase() ?? '';
      final searchLower = _searchQuery.toLowerCase();

      return displayName.contains(searchLower) ||
          workEmail.contains(searchLower) ||
          city.contains(searchLower) ||
          addressLine1.contains(searchLower);
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final notifier = ref.read(employeesProvider.notifier);
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (notifier.hasMore && !notifier.isLoading) {
        notifier.fetchNextPage();
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final employeesAsync = ref.watch(allEmployeesProvider);
        final employees = ref.watch(employeesProvider);
    final notifier = ref.read(employeesProvider.notifier);
    final filteredEmployees = _getFilteredEmployees(employees);


    return Stack(
      children: [
        PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:SecondaryScaffoldWithAppBar(
          context,
          'People',
          SvgImage24x24('assets/images/appbackbutton.svg'),
          () => Navigator.pop(context),
          showSearchIcon: !_showSearchField,
          showHelpIcon: true,
          showSearchField: _showSearchField,
          toggleSearch: () {
            setState(() {
              _showSearchField = true;
            });
          },
          searchWidget: SearchField(
            controller: _searchController,
            hintText: 'Name, department, job title, location',
            onClose: () {
              setState(() {
                _searchController.clear();
                _showSearchField = false;
                _searchQuery = '';
              });
            },
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          bodyItem: Padding(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              bottom: MediaQuery.of(context).padding.bottom,
            ),
            child: Column(
              children: [
                CustomTabSelector(
                  labels: ['Directory', 'Birthdays'],
                  initialIndex: _selectedTab,
                  onTabChanged: (index) {
                    setState(() {
                      _selectedTab = index;
                      _selectedBirthdayDate = null;
                    });
                  },
                ),
                Expanded(
  child: _selectedTab == 0
      ? DirectorySection(
          people: filteredEmployees,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
          controller: _scrollController,
          isLoading: notifier.isLoading,
          hasMore: notifier.hasMore,
        )
      : BirthdaySection(
          employees: filteredEmployees,
          onCalendarTap: _showCalendarDialog,
          selectedDate: _selectedBirthdayDate,
          onClearSelectedDate: () => setState(() => _selectedBirthdayDate = null),
        ),
)

          //       Expanded(
          //         child: employees.when(
          //           data: (employees) {
          //             final filteredEmployees = _getFilteredEmployees(
          //               employees,
          //             );

          //             return _selectedTab == 0
          //                 ? DirectorySection(
          //                   people: filteredEmployees,
          // searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
          // controller: _scrollController,
          // isLoading: notifier.isLoading,
          // hasMore: notifier.hasMore,
          //                 )
          //                 : BirthdaySection(
          //                   employees: filteredEmployees,
          //                   onCalendarTap: _showCalendarDialog,
          //                   selectedDate: _selectedBirthdayDate,
          //                   onClearSelectedDate:
          //                       () => setState(() {
          //                         _selectedBirthdayDate = null;
          //                       }),
          //                 );
          //           },
          //           loading:
          //               () => Center(
          //                 child: CircularProgressIndicator(
          //                   color: AppColors.viewColor,
          //                 ),
          //               ),
          //           error:
          //               (error, stackTrace) => Center(
          //                 child: Column(
          //                   mainAxisAlignment: MainAxisAlignment.center,
          //                   children: [
          //                     Icon(
          //                       Icons.error_outline,
          //                       size: 48,
          //                       color: AppColors.blackColor.withOpacity(0.6),
          //                     ),
          //                     SizedBox(height: 16),
          //                     Text(
          //                       'Error loading data',
          //                       style: TextStyle(
          //                         fontSize: 16,
          //                         fontWeight: FontWeight.w600,
          //                         color: AppColors.blackColor.withOpacity(0.8),
          //                       ),
          //                     ),
          //                     SizedBox(height: 16),
          //                     ElevatedButton(
          //                       onPressed:
          //                           () => ref.refresh(allEmployeesProvider),
          //                       child: Text('Retry'),
          //                     ),
          //                   ],
          //                 ),
          //               ),
          //         ),
          //       ),
              ],
            ),
          ),
          bottomNavigationBar: CustomBottomNavigationBar(
            onTap: (index) => setState(() => _selectedIndex = index),
          ),
         ) ),

        // Full screen calendar overlay
        if (_showCalendar)
          Material(
            color: Colors.transparent,
            child: Stack(
              children: [
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () => setState(() => _showCalendar = false),
                    child: Container(
                      color: AppColors.blackColor.withOpacity(0.5),
                    ),
                  ),
                ),
                Center(
  child: employees.isEmpty && notifier.isLoading
      ? Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: CircularProgressIndicator(),
        )
      : CalendarWidget(
          birthdayDates: _getBirthdayDates(employees),
          onClose: () => setState(() => _showCalendar = false),
          onDateSelected: (date) {
            setState(() {
              _selectedBirthdayDate = date;
              _showCalendar = false;
            });
          },
        ),
)

                // Center(
                //   child: employeesAsync.when(
                //     data:
                //         (employees) => CalendarWidget(
                //           birthdayDates: _getBirthdayDates(employees),
                //           onClose: () => setState(() => _showCalendar = false),
                //           onDateSelected: (date) {
                //             setState(() {
                //               _selectedBirthdayDate = date;
                //               _showCalendar = false;
                //             });
                //           },
                //         ),
                //     loading:
                //         () => Container(
                //           padding: EdgeInsets.all(20),
                //           decoration: BoxDecoration(
                //             color: AppColors.whiteColor,
                //             borderRadius: BorderRadius.circular(12),
                //           ),
                //           child: CircularProgressIndicator(),
                //         ),
                //     error:
                //         (error, stackTrace) => Container(
                //           padding: EdgeInsets.all(20),
                //           decoration: BoxDecoration(
                //             color: AppColors.whiteColor,
                //             borderRadius: BorderRadius.circular(12),
                //           ),
                //           child: Text('Error loading calendar'),
                //         ),
                //   ),
                // ),
              ],
            ),
          ),
      ],
    );
  }
}

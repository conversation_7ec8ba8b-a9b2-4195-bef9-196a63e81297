import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/account/asset/components/assetCard.dart';
import 'package:seawork/screens/account/asset/components/assetDetailSheet%20.dart';
import 'package:seawork/screens/account/asset/models/assetItemModel.dart';
import 'package:seawork/screens/account/asset/provider/assetProvider.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';
import 'package:seawork/utils/style/colors.dart';

class MyAssetScreen extends ConsumerWidget {
  const MyAssetScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final myAssetsRepository = ref.watch(myAssetsRepositoryProvider);
    final assetFuture = myAssetsRepository.getMyAssetDetails();

 return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: FutureBuilder<MyAssetDetailModel?>(
        future: assetFuture,
        builder: (context, snapshot) {
          // Determine showSearchIcon based on the current state
          bool showSearchIcon = true;
          if (snapshot.connectionState == ConnectionState.done) {
            if (!snapshot.hasData || snapshot.data == null) {
              showSearchIcon =
                  false; // Hide search when "No asset data available" is showing
            }
          }

          return Scaffold(
            backgroundColor: AppColors.secondaryColor,
            appBar: CustomAppBar(
              title: 'My Assets',
              showSearchIcon: showSearchIcon,
              showActionIcon: false,
              onBackPressed: () {
              context.pop();
            },),
            body: FutureBuilder<MyAssetDetailModel?>(
              future: assetFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else if (!snapshot.hasData || snapshot.data == null) {
                  return const Center(child: Text('No asset data available'));
                }

                final assetData = snapshot.data!;
                return AssetCard(
                  asset: assetData,
                  onTap: () => _showAssetDetails(context, assetData),
                );
              },
            ),
            bottomNavigationBar: CustomBottomNavigationBar(
              onTap: (index) {
                // Handle navigation if needed
              },
            ),
          );
        },
      ),
    );
  }

  void _showAssetDetails(BuildContext context, MyAssetDetailModel asset) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AssetDetailsBottomSheet(asset: asset),
    );
  }
}

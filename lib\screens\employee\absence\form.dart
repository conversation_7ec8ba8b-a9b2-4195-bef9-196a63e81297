import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/customSubmitConfirmationDialog.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/employee/absence/LeaveConstants/LeaveConstants.dart';
import 'package:seawork/screens/employee/absence/absenceSelector.dart';

import 'package:seawork/screens/employee/absence/applyLeave.dart';
import 'package:seawork/screens/employee/absence/components/customAbsenceBalncer.dart';
import 'package:seawork/screens/employee/absence/components/escotLeaveWidget.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';

import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/employee/absence/saveDraftPopup.dart';
import 'package:seawork/screens/employee/absence/selectLeaveTypeScreen.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/style/sizeConfig.dart';
import 'package:seawork/utils/util.dart';

// ignore: must_be_immutable
class FormLeave extends ConsumerStatefulWidget {
  String leaveType;
  dynamic leaveTypes;
  String? date1;
  String? date2;
  String? remarks;
  String? Status_Tab;
  bool? isDraft;
  bool? isEdit;
  AbsenceItem? absence;
  FormLeave({
    Key? key,
    this.date1,
    this.date2,
    this.absence,
    this.isDraft,
    this.remarks,
    this.isEdit,
    this.leaveTypes,
    this.Status_Tab,
    required this.leaveType,
  }) : super(key: key);

  @override
  ConsumerState<FormLeave> createState() => _LeaveRequestFormState();
}

class _LeaveRequestFormState extends ConsumerState<FormLeave> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _attachmentController = TextEditingController();
  final TextEditingController _pasteLinkController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();
  bool isFormValid = false;
  String? _selectedAbsenceType;
  String? _reason;
  int durationInDays = 0;
  DateTime? startDate;
  DateTime? endDate;
  bool _hasFormChanges = false;
  String selectedSelfOption = 'Yes';
  bool isFormFilled = false;
  int? projectedBalance = 5;
  double projectedbalance = 0.0;
  String? absencesUniqID;
  dynamic attachmentIDS;
  dynamic AbsenceAttachments;
  bool isVisible = false; // Initially hidden
  bool _isLoading = false;
  Map<String, bool> _loadingStates = {};
  List<Map<String, dynamic>> AbsenceAttachmentsList = []; // your actual model
  List<dynamic> ids = []; // the corresponding attachment IDs

  List<FileModel> selectedFiles = [];
  List<FileModel> uploadedFiles = [];
  Map<String, dynamic> originalData = {};
  bool _isDropdownOpen = false;
  final ScrollController scrollController = ScrollController();

  void _updateFiles(List<FileModel> files) {
    setState(() {
      uploadedFiles = files;
    });
    Future.microtask(() => validateForm());
  }

  Map<String, dynamic> getUpdatedFields() {
    Map<String, dynamic> updatedFields = {};
    String formattedStartDate = DateFormat('yyyy-MM-dd').format(startDate!);
    String formattedEndDate = DateFormat('yyyy-MM-dd').format(endDate!);
    if (_selectedAbsenceType != null &&
        _selectedAbsenceType != originalData["absenceType"]) {
      updatedFields["absenceType"] = _selectedAbsenceType;
    }

    if (_startDateController.text.isNotEmpty &&
        _startDateController.text != originalData["startDate"]) {
      updatedFields["startDate"] = formattedStartDate;
    }
    updatedFields["absenceStatusCd"] =
        widget.isEdit! && widget.isDraft! ? "SAVED" : "SUBMITTED";

    if (_endDateController.text.isNotEmpty &&
        _endDateController.text != originalData["endDate"]) {
      updatedFields["endDate"] = formattedEndDate;
    }

    if (_commentsController.text.isNotEmpty &&
        _commentsController.text != originalData["comment"]) {
      updatedFields["comments"] = _commentsController.text;
    }

    if (_reason!.isNotEmpty && _reason != originalData["absenceReason"]) {
      updatedFields["absenceReason"] = _reason;
    }

    return updatedFields;
  }

  Future<void> submitLeaveRequest(
    BuildContext context,
    WidgetRef ref,
    bool? isDraft,
  ) async {
    ref.read(isSubmittingProvider.notifier).state = true; // Start Loading
    String currentTime = DateFormat('HH:mm').format(DateTime.now());
    String formattedStartDate = DateFormat('yyyy-MM-dd').format(startDate!);
    String formattedEndDate = DateFormat('yyyy-MM-dd').format(endDate!);
    // 🔹 Loop through each uploaded file and create attachments
    List<Map<String, String>> attachments =
        uploadedFiles.map((file) {
          return {
            "DatatypeCode": "FILE",
            "FileContents": base64Encode(file.bytes),
            "Description": "Leave Certificate",
            "CategoryName": "Leave", // Correcting to match your example payload
            "FileName": file.name, // Corrected file name
          };
        }).toList();

    // 🔹 List of leave types that **require** attachments

    if (leaveTypesRequiringAttachments.contains(widget.leaveType) &&
        attachments.isEmpty) {
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Attachments are required for this leave type."),
        ),
      );
      return;
    } else if (widget.leaveType == 'Sick leave' &&
        durationInDays > 1 &&
        attachments.isEmpty) {
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Attachments are required for this leave type."),
        ),
      );
      return;
    }

    // 🔹 Create the request payload
    Map<String, dynamic> LeaveData = {
      // "personNumber": masriPersonNumber,// Todo verify if this works
      "employer": employer,
      "absenceType": widget.leaveType,
      "startDate": formattedStartDate,
      "startTime": currentTime,
      "endDate": formattedEndDate,
      "endTime": currentTime,
      "startDateDuration": 1,
      "endDateDuration": 1,
      if (isDraft == true) "absenceStatusCd": "SAVED",
      "approvalStatusCd": isDraft == true ? "" : "SUBMITTED",
      "comments": _commentsController.text,
      "absenceReason": _reason,
      "absenceAttachments":
          attachments, // attachments.cast<Map<String, String>>()
    };

    AbsenceRequest LeaveRequest = AbsenceRequest.fromAbsenceType(
      widget.leaveType,
      LeaveData,
    );

    try {
      await ref.read(leaveRequestProvider(LeaveRequest).future);
      await showDialog(
        context: context,
        builder: (context) {
          return CustomSubmitConfirmationDialogBox(
            date1: startDate!,
            type: widget.leaveType,
            date2: endDate!,
            isDraft: isDraft ?? false,
            onClose: () {
              context.push('/apply-leave');
              ref
                  .read(awaitingListNotifierProvider.notifier)
                  .fetchAllAbsences();
            },
          );
        },
      );

      _startDateController.clear();
      _endDateController.clear();
      _remarksController.clear();
      _commentsController.clear();
      _attachmentController.clear();
      _pasteLinkController.clear();
      _reasonController.clear();

      ref.invalidate(getAbsenceProvider);
      ref.invalidate(taskItemsProvider);
      ref.read(isSubmittingProvider.notifier).state = false;

      context.push('/apply-leave');
    } on DioException catch (e) {
      debugPrint("Caught DioException: $e");

      String errorMessage = 'Something went wrong';
      if (e.response != null && e.response?.data != null) {
        if (e.response?.data is Map && e.response?.data['details'] != null) {
          errorMessage = e.response?.data['details'];
        } else {
          errorMessage = e.response.toString();
        }
      } else if (e.message != null) {
        errorMessage = e.message!;
      }

      Fluttertoast.showToast(
        msg: "Error: $errorMessage",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 3,
        backgroundColor: AppColors.formToast, // dark grey for a sleek look
        textColor: AppColors.whiteColor,
        fontSize: 15.0,
      );

      // Show the error message in SnackBar
      // if (context.mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(content: Text("Error: $errorMessage")),
      //   );
      // }
    } catch (e, st) {
      debugPrint("Caught unknown exception: $e\n$st");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("An unexpected error occurred. ")),
        );
      }
    } finally {
      ref.invalidate(taskItemsProvider);
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
    }
  }

  Future<void> updateLeaveRequest(WidgetRef ref, BuildContext context) async {
    ref.read(isSubmittingProvider.notifier).state = true;
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    Map<String, dynamic> updatedFields =
        getUpdatedFields(); // Get only modified fields

    if (updatedFields.isEmpty) {
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text("No changes detected.")),
      );
      return;
    }

    List<Map<String, Object>> attachments =
        uploadedFiles.map((file) {
          return {
            "DatatypeCode": "FILE",
            "file": file,
            "Description": "Leave Certificate",
            "CategoryName": "Leave",
            "FileName": file.name,
          };
        }).toList();

    if (leaveTypesRequiringAttachments.contains(widget.leaveType) &&
        AbsenceAttachments.isEmpty &&
        uploadedFiles.isEmpty) {
      ref.read(isSubmittingProvider.notifier).state = false;
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text("Attachments are required for this leave type."),
        ),
      );
      return;
    }

    // Merge attachments into updated fields

    try {
      final params = (
        absenceRequest: updatedFields,
        absenceUniqID: widget.absence?.personAbsenceEntryId.toString() ?? '',
      );

      // 🔹 Step 1: Upload Attachments First
      if (attachments.isNotEmpty &&
          widget.absence?.personAbsenceEntryId != null) {
        final absenceUniqID = widget.absence!.personAbsenceEntryId.toString();

        // Wait for the upload to complete
        await ref.read(
          uploadAttachmentProvider((attachments, absenceUniqID)).future,
        );
      } else {}

      await ref.read(
        leaveRequestupdateProvider((
          absenceRequest: updatedFields,
          absenceUniqID: widget.absence?.personAbsenceEntryId.toString() ?? '',
        )).future,
      );

      // await Future.delayed(const Duration(seconds: 3)); // Optional short delay
      await showDialog(
        context: context,
        builder: (context) {
          return CustomSubmitConfirmationDialogBox(
            date1: startDate!,
            type: widget.leaveType,
            date2: endDate!,
            isDraft: widget.isDraft ?? false,
            onClose: () {
              context.push('/apply-leave');

              ref
                  .read(awaitingListNotifierProvider.notifier)
                  .fetchAllAbsences();
            },
          );
        },
      );

      _startDateController.clear();
      _endDateController.clear();
      _remarksController.clear();
      _commentsController.clear();
      _attachmentController.clear();
      _pasteLinkController.clear();
      _reasonController.clear();

      ref.invalidate(getAbsenceProvider);
      ref.invalidate(taskItemsProvider);
      ref.read(isSubmittingProvider.notifier).state = false;

      context.push('/apply-leave');
    } on DioException catch (e) {
      debugPrint("Caught DioException: $e");

      String errorMessage = 'Something went wrong';
      if (e.response != null && e.response?.data != null) {
        if (e.response?.data is Map && e.response?.data['details'] != null) {
          errorMessage = e.response?.data['details'];
        } else {
          errorMessage = e.response.toString();
        }
      } else if (e.message != null) {
        errorMessage = e.message!;
      }
      Fluttertoast.showToast(
        msg: "Error: $errorMessage",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 3,
        backgroundColor: AppColors.formToast, // dark grey for a sleek look
        textColor: AppColors.whiteColor,
        fontSize: 15.0,
      );
      // Show the error message in SnackBar
      // if (context.mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(content: Text("Error: $errorMessage")),
      //   );
      // }
    } catch (e, st) {
      debugPrint("Caught unknown exception: $e\n$st");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("An unexpected error occurred. ")),
        );
      }
    } finally {
      ref.invalidate(taskItemsProvider);
      if (context.mounted) {
        ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      }
    }
  }

  @override
  void initState() {
    super.initState();

    if (widget.absence != null) {
      originalData = {
        "absenceType": widget.absence!.absenceType,
        "startDate": widget.absence!.startDate,
        "endDate": widget.absence!.endDate,
        "comment": widget.absence!.comment,
        "absenceReason": widget.absence!.absenceReason,
        "absenceAttachments": widget.absence!.absenceAttachments,
      };

      setState(() {
        AbsenceAttachments = widget.absence!.absenceAttachments;
        ids =
            widget.absence!.absenceAttachments!
                .map((a) => a.attachedDocumentId)
                .toList();

        absencesUniqID = widget.absence!.personAbsenceEntryId.toString();
        print('originalData: $originalData');
        if (widget.absence!.startDate != null &&
            widget.absence!.endDate != null) {
          startDate = DateTime.parse(widget.absence!.startDate!);
          endDate = DateTime.parse(widget.absence!.endDate!);
          isVisible = true;
          startDate = DateTime.parse(
            DateFormat('yyyy-MM-dd').format(startDate!),
          );
          endDate = DateTime.parse(DateFormat('yyyy-MM-dd').format(endDate!));

          _startDateController.text = DateFormat(
            'dd/MM/yyyy',
          ).format(startDate!);
          _endDateController.text = DateFormat('dd/MM/yyyy').format(endDate!);
        }

        if (widget.absence!.comment != null) {
          _commentsController.text = widget.absence!.comment!;
          _remarksController.text = widget.absence!.comment!;
        }

        _reason = widget.absence!.absenceReason ?? '';
        AbsenceAttachments = widget.absence!.absenceAttachments ?? [];
      });
    }

    if (widget.leaveType.isNotEmpty) {
      _selectedAbsenceType = widget.leaveType;
    }

    _startDateController.addListener(validateForm);
    _endDateController.addListener(validateForm);
    _startDateController.addListener(_onFormChanged);
    _endDateController.addListener(_onFormChanged);
    _remarksController.addListener(_onFormChanged);
    _remarksController.addListener(validateForm);
    _commentsController.addListener(_onFormChanged);
    _commentsController.addListener(validateForm);
    _attachmentController.addListener(_onFormChanged);
    _attachmentController.addListener(validateForm);
    _pasteLinkController.addListener(_onFormChanged);
    _pasteLinkController.addListener(validateForm);
    _commentsController.addListener(_onFormChanged);
    _commentsController.addListener(validateForm);
  }

  @override
  void dispose() {
    _startDateController.dispose();
    _endDateController.dispose();
    _remarksController.dispose();
    _commentsController.dispose();
    _attachmentController.dispose();
    _pasteLinkController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    setState(() {
      _hasFormChanges = _checkForChanges();
      // Also validate the form whenever any form field changes
      validateForm();
    });
  }

  bool _checkForChanges() {
    return _selectedAbsenceType != null ||
        _startDateController.text.isNotEmpty ||
        _endDateController.text.isNotEmpty ||
        _remarksController.text.isNotEmpty ||
        _attachmentController.text.isNotEmpty ||
        _commentsController.text.isNotEmpty ||
        uploadedFiles.isNotEmpty ||
        _pasteLinkController.text.isNotEmpty;
  }

  void validateForm() {
    bool valid = true;

    if (_selectedAbsenceType == null || _selectedAbsenceType!.isEmpty) {
      valid = false;
    }

    if (_startDateController.text.isEmpty || _endDateController.text.isEmpty) {
      valid = false;
    }

    if ((widget.leaveType == 'Bereavement (1st Degree)' ||
            widget.leaveType == 'Bereavement (2nd Degree)' ||
            widget.leaveType == 'Escort leave') &&
        _reason == null) {
      valid = false;
    }

    // Only do date-based checks if dates are filled
    if (_startDateController.text.isNotEmpty &&
        _endDateController.text.isNotEmpty) {
      DateTime startDate = DateFormat(
        'yyyy/MM/dd',
      ).parse(_startDateController.text);
      DateTime endDate = DateFormat(
        'yyyy/MM/dd',
      ).parse(_endDateController.text);
      int durationInDays = endDate.difference(startDate).inDays;

      if (widget.leaveType == 'Sick Leave' &&
          durationInDays > 1 &&
          uploadedFiles.isEmpty) {
        valid = false;
      }

      bool isAttachmentRequired = leaveTypesRequiringAttachments.contains(
        widget.leaveType,
      );

      if (isAttachmentRequired) {
        final hasExisting =
            AbsenceAttachments != null && AbsenceAttachments!.isNotEmpty;
        final hasNew = uploadedFiles.isNotEmpty;
        final isEdit = widget.isEdit ?? false;

        if ((isEdit && !hasExisting && !hasNew) || (!isEdit && !hasNew)) {
          valid = false;
        }
      }

      // bool isOriginalAttachmentEmpty =
      //     originalData["absenceAttachments"] == null ||
      //     originalData["absenceAttachments"].isEmpty;
      // if (isAttachmentRequired &&
      //     (AbsenceAttachments.isEmpty || uploadedFiles.isEmpty)) {
      //   valid = false;
      // }
    }

    // Set final form validation result
    setState(() {
      isFormValid = valid;

      // Reformat dates
      if (_startDateController.text.isNotEmpty) {
        DateTime startDate = DateFormat(
          'yyyy-MM-dd',
        ).parse(_startDateController.text);
        _startDateController.text = DateFormat('yyyy/MM/dd').format(startDate);
      }

      if (_endDateController.text.isNotEmpty) {
        DateTime endDate = DateFormat(
          'yyyy-MM-dd',
        ).parse(_endDateController.text);
        _endDateController.text = DateFormat('yyyy/MM/dd').format(endDate);
      }
    });
  }

  Future<void> _showSaveDraftConfirmationDialog() async {
    final result = await showDialog(
      context: context,
      builder:
          (context) => SaveDraftConfirmationDialog(
            onSaveDraft: () async {
              try {
                ref.read(isSubmittingProvider.notifier).state =
                    true; // Start Loading

                if (isFormValid && projectedbalance >= 0) {
                  await submitLeaveRequest(context, ref, true);
                }
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => ApplyLeave()),
                );
              } catch (e) {
                ref.read(isSubmittingProvider.notifier).state =
                    false; // Start Loading
              }
            },
            onDelete: () {
              Navigator.pop(context); // This will close the dialog
              Navigator.pop(context); // This will go back to ApplyLeave
            },
          ),
    );

    if (result != null) {
      Navigator.pop(context);
    }
  }

  void _showLeaveTypesBottomSheet(BuildContext context) {
    const List<String> leaveTypes = [
      'Annual leave',
      'Bereavement (1st Degree)',
      'Bereavement (2nd Degree)',
      'Business mission',
      'COVID leave',
      'Emergency leave',
      'Escort leave',
      'Hajj leave',
      'Long sick leave',
      'Maternity leave',
      'Paternity leave',
      'Sick leave',
      'Study leave',
      'Time of in lieu',
      'Unpaid leave',
      'Work from home',
    ];

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      backgroundColor: AppColors.whiteColor,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
            color: AppColors.whiteColor,
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 12, bottom: 12),
                child: Container(
                  width: 30.w,
                  height: 5.h,
                  decoration: BoxDecoration(
                    color: AppColors.viewColor,
                    borderRadius: BorderRadius.circular(9),
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: leaveTypes.length,
                  itemBuilder: (context, index) {
                    final bool isSelected =
                        widget.leaveTypes.contains(leaveTypes[index]) ||
                        leaveTypes[index] == widget.leaveType;
                    return InkWell(
                      onTap: () {
                        Navigator.pop(context); // Close the bottom sheet

                        // Navigate to a new form with the selected leave type
                        if (leaveTypes[index] != widget.leaveType) {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            constraints: BoxConstraints(
                              maxHeight:
                                  MediaQuery.of(context).size.height * 0.95,
                            ),
                            builder: (context) {
                              return FormLeave(
                                leaveType: leaveTypes[index],
                                date1:
                                    _startDateController.text.isNotEmpty
                                        ? _startDateController.text
                                        : null,
                                date2:
                                    _endDateController.text.isNotEmpty
                                        ? _endDateController.text
                                        : null,
                                remarks:
                                    _remarksController.text.isNotEmpty
                                        ? _remarksController.text
                                        : null,
                              );
                            },
                          );
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          vertical: 16.0,
                          horizontal: 24.0,
                        ),
                        margin: isSelected ? const EdgeInsets.all(2.0) : null,
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppColors.formBoxDecorationColor
                                  : AppColors.whiteColor,
                          borderRadius:
                              isSelected ? BorderRadius.circular(4.0) : null,
                        ),
                        child: OpenSansText(
                          leaveTypes[index],
                          color:
                              isSelected
                                  ? AppColors.viewColor
                                  : AppColors.blackColor,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);

    if (widget.absence != null) {
      final absencesUniqID = widget.absence!.personAbsenceEntryId ?? '';
      attachmentIDS = ref.watch(
        getAbsenceByUniqIDProvider(absencesUniqID.toString()),
      );
    }
    final isSubmitting = ref.watch(isSubmittingProvider);

    final planBalancesAsync = ref.watch(getPlanBalanceProvider);

    // Extract balance based on selected leave type
    double? selectedBalance;
    double? AbsenceBalance;
    // Store previous fetched items
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    void handlePlanBalances(
      AsyncValue<List<PlanBalanceModel>> planBalancesAsync,
      String leaveType,
      Function(double?) onBalanceUpdated,
    ) {
      planBalancesAsync.when(
        data: (planBalances) {
          // Ensure that planBalances is a List<PlanBalanceModel>
          if (planBalances.isEmpty) {
            onBalanceUpdated(-1);
            return;
          }

          // Check if any plan matches the leaveType
          final hasMatchingPlan = planBalances.any(
            (plan) =>
                plan.planName.toString().toLowerCase().trim() ==
                leaveType.toString().toLowerCase().trim(),
          );

          if (!hasMatchingPlan) {
            // If no matching plan found, set balance to -1
            onBalanceUpdated(-1);
            return;
          }

          // Find the matching plan
          final matchingPlan = planBalances.firstWhere(
            (plan) =>
                plan.planName.toString().toLowerCase().trim() ==
                leaveType.toString().toLowerCase().trim(),
          );

          // Compute AbsenceBalance
          final selectedBalance =
              matchingPlan.balanceAsOfBalanceCalculationDate;
          final absenceBalance =
              selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

          // Pass the balance back via callback function
          onBalanceUpdated(absenceBalance as double?);
        },
        loading: () => const Center(child: CustomLoadingWidget()),
        error: (err, stack) {
          onBalanceUpdated(-1);
        },
      );
    }

    handlePlanBalances(ref.watch(getPlanBalanceProvider), widget.leaveType, (
      balance,
    ) {
      setState(() {
        AbsenceBalance = balance;
      });
    });

    Widget buildAttachmentContainer(
      attachment,
      double screenWidth,
      double screenHeight,
      bool isFirstContainer,
      String attachmentId,
      List<dynamic> ids,
    ) {
      return Padding(
        padding: EdgeInsets.only(top: screenHeight * 0.01),
        child: GestureDetector(
          onTap: () async {
            String _getMimeTypeFromFileName(String fileName) {
              final ext = fileName.split('.').last.toLowerCase();
              switch (ext) {
                case 'pdf':
                  return 'application/pdf';
                case 'png':
                  return 'image/png';
                case 'jpg':
                case 'jpeg':
                  return 'image/jpeg';
                case 'gif':
                  return 'image/gif';
                case 'txt':
                  return 'text/plain';
                case 'mp4':
                  return 'video/mp4';
                default:
                  return 'application/octet-stream';
              }
            }

            setState(() => _loadingStates[attachmentId] = true);

            try {
              // Get the repository from Riverpod using ref (not context)
              final repo = ref.read(leaveTypesRepositoryProvider);
              // Use the file name or fallback
              final fileName = attachment.fileName ?? 'attachment.pdf';
              final contentType = _getMimeTypeFromFileName(fileName);
              await repo.downloadAndOpenAbsenceAttachment(
                absencesUniqID:
                    widget.absence?.personAbsenceEntryId.toString() ?? '',
                absenceAttachmentsUniqID: attachmentId,
                fileName: fileName,
                contentType: contentType,
              );
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to download: \\${e.toString()}'),
                ),
              );
            } finally {
              if (!mounted) return;
              setState(() => _loadingStates[attachmentId] = false);
            }
          },
          child: Container(
            width: isFirstContainer ? screenWidth * 0.43 : screenWidth * 0.31,
            height: screenHeight * 0.045,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.lightGreyColor2, width: 1.0),
              color: Colors.transparent,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width:
                      isFirstContainer
                          ? screenWidth * 0.03
                          : screenWidth * 0.02,
                ),
                Image.asset(
                  'assets/images/document.png',
                  width: screenWidth * 0.05,
                  height: screenHeight * 0.05,
                ),
                SizedBox(width: screenWidth * 0.01),
                Container(
                  width:
                      isFirstContainer
                          ? screenWidth * 0.20
                          : screenWidth * 0.10,
                  height: screenHeight * 0.02,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3),
                    color: AppColors.formBoxDecorationColor2,
                  ),
                  child: Center(
                    child: Text(
                      (attachment.fileName ?? 'Unknown.pdf'),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: screenWidth * 0.02,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                SizedBox(width: screenWidth * 0.02),
                _loadingStates[attachmentId] == true
                    ? CustomLoadingWidget(
                      height: 17,
                      width: 17,
                      strokeWidth: 2.0,
                    )
                    : Image.asset(
                      isFirstContainer
                          ? 'assets/images/icon_download_2.png'
                          : 'assets/images/download_icon.png',
                      height: 17,
                      width: 17,
                    ),
                SizedBox(width: screenWidth * 0.01), // Spacing between icons
                if (ids.length > 1)
                  GestureDetector(
                    onTap: () async {
                      try {
                        setState(() {
                          _loadingStates[attachmentId] =
                              true; // Start loading for selected attachment
                        });
                        await ref.read(
                          deleteAttachmentProvider((
                            attachmentId,
                            absencesUniqID!,
                          )).future,
                        );
                        // Remove the attachment from the list
                        setState(() {
                          int indexToRemove = ids.indexOf(attachmentId);
                          if (indexToRemove != -1) {
                            setState(() {
                              AbsenceAttachments.removeAt(indexToRemove);
                              ids.removeAt(indexToRemove);
                              _loadingStates.remove(attachmentId);
                            });
                          }

                          ref.invalidate(
                            getAbsenceByUniqIDProvider(
                              absencesUniqID.toString(),
                            ),
                          );

                          _loadingStates.remove(
                            attachmentId,
                          ); // Remove loading state
                        });

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text("Attachment Deleted")),
                        );
                      } catch (e) {
                        setState(() {
                          _loadingStates[attachmentId] =
                              false; // Start loading for selected attachment
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text("Failed to delete: $e")),
                        );
                      } finally {
                        setState(() {
                          _loadingStates.remove(
                            attachmentId,
                          ); // Remove loading state after completion
                        });
                      }
                    },
                    child:
                        _loadingStates[attachmentId] == true
                            ? SizedBox(
                              width: 17,
                              height: 17,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: AppColors.red,
                              ),
                            )
                            : Icon(
                              Icons.close,
                              color: AppColors.viewColor,
                              size: 17,
                            ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async {
        if (_hasFormChanges) {
          await _showSaveDraftConfirmationDialog();
          return false;
        }
        // Navigate to ApplyLeavePage
        Navigator.pop(context); // This will go back to ApplyLeave
        return true;
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          title: 'New leave',
          showActionIcon: true,
          onBackPressed: () {
            context.pop();
          },
        ),
        body: SafeArea(
          child: Column(
            children: [
              if (widget.leaveType == "Escort leave")
                WidgetEscortLeave(context)
              else
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20.0, 16.0, 20.0, 10.0),
                    child: Form(
                      key: _formKey,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: ListView(
                          children: [
                            Column(
                              children: [
                                HeadingText(
                                  text: 'Absence type',
                                  hasAsterisk: true,
                                ),
                                const SizedBox(height: 8),
                                GestureDetector(
                                  onTap: () async {
                                    setState(() => _isDropdownOpen = true);
                                    final selected = await showModalBottomSheet<
                                      String
                                    >(
                                      context: context,
                                      isScrollControlled: true,
                                      backgroundColor: Colors.transparent,
                                      isDismissible: true,
                                      enableDrag: true,
                                      builder: (context) {
                                        return DraggableScrollableSheet(
                                          initialChildSize: 0.6,
                                          minChildSize: 0.3,
                                          maxChildSize: 0.85,
                                          expand: false,
                                          builder: (context, scrollController) {
                                            return ClipRRect(
                                              borderRadius:
                                                  const BorderRadius.only(
                                                    topLeft: Radius.circular(
                                                      20,
                                                    ),
                                                    topRight: Radius.circular(
                                                      20,
                                                    ),
                                                  ),
                                              child: LeaveTypesForNavigation(
                                                leaveTypes: widget.leaveTypes,
                                                scrollController:
                                                    scrollController,
                                                selectedLeaveType:
                                                    widget.leaveType,
                                                isFromDashboard:
                                                    false, // Not from dashboard
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    );
                                    setState(() => _isDropdownOpen = false);
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColors.whiteColor,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: AppColors.lightGreyColor2,
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: OpenSansText(
                                            capitalizeFirstWordOnly(
                                                  widget.leaveType,
                                                ) ??
                                                'Select',
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            color: AppColors.blackColor,
                                          ),
                                        ),
                                        Transform.rotate(
                                          angle: _isDropdownOpen ? 3.14159 : 0,
                                          child: CustomSvgImage(
                                            imageName: "ArrowLeft",
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            if (widget.leaveType == 'Long sick leave' ||
                                widget.leaveType == 'Study leave')
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [EscortLeave()],
                              ),
                            if (widget.leaveType != 'Long sick leave' &&
                                widget.leaveType != 'Study leave' &&
                                AbsenceBalance != null) ...[
                              AbsenceBalanceWidget(
                                absenceType: '',
                                balance: AbsenceBalance,
                              ),
                            ],
                            if (widget.leaveType != 'Long sick leave' &&
                                widget.leaveType != 'Study leave') ...[
                              // const SizedBox(height: 24),
                              CustomDatePickerField(
                                enabled: !isSubmitting,
                                dateText: "Start date",
                                hintText: "Select start date",
                                controller: _startDateController,
                                onDateSelected: (selectedDate) {
                                  setState(() {
                                    startDate = selectedDate;
                                  });
                                },
                                isStartDate: true,
                              ),
                              const SizedBox(height: 24),
                              CustomDatePickerField(
                                enabled: !isSubmitting,
                                dateText: "End date",
                                hintText: "Select end date",
                                controller: _endDateController,
                                onDateSelected: (selectedDate) {
                                  setState(() {
                                    endDate = selectedDate;
                                  });
                                  if (startDate != null &&
                                      selectedDate.isBefore(startDate!)) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'End date cannot be earlier than start date.',
                                        ),
                                        backgroundColor: AppColors.red,
                                      ),
                                    );
                                    // Optionally clear the end date
                                    setState(() {
                                      endDate = null;
                                      _endDateController.clear();
                                    });
                                  }
                                },
                                isStartDate: false,
                                startDate: startDate,
                              ),
                              const SizedBox(height: 6),
                              if (startDate != null &&
                                  endDate != null &&
                                  AbsenceBalance != null)
                                DurationBalanceWidget(
                                  onProjectedBalanceUpdated: (p0) {
                                    projectedbalance = p0;
                                  },
                                  absenceBalance: AbsenceBalance!,
                                  startDate: startDate!,
                                  endDate: endDate!,
                                  onDurationCalculated: (p0) {
                                    durationInDays = p0;
                                  },
                                ),
                            ],
                            //
                            // const SizedBox(height: 8),
                            // if (projectedBalance != null &&
                            //     durationInDays > projectedBalance!)
                            //   Column(
                            //     children: [
                            //       SizedBox(height: 8.h),
                            //       Row(
                            //         children: [
                            //           Image.asset(
                            //             'assets/images/Infoicon.png',
                            //             height: 18,
                            //             width: 19,
                            //           ),
                            //           SizedBox(width: 5.w),
                            //           Text(
                            //             'Leave balance exceeded. Please adjust your dates.',
                            //             style: const TextStyle(
                            //               fontFamily: 'Inter',
                            //               fontSize: 12,
                            //               fontWeight: FontWeight.w400,
                            //               color: AppColors.Orange,
                            //             ),
                            //           ),
                            //         ],
                            //       ),
                            //     ],
                            //   ),
                            if (widget.leaveType ==
                                    'Bereavement (1st degree)' ||
                                widget.leaveType == 'Bereavement (2nd degree)')
                              Column(
                                children: [
                                  SizedBox(height: 20),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      HeadingText(
                                        text: 'Reason',
                                        hasAsterisk: true,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  AbsenceSelector(
                                    enabled: !isSubmitting,
                                    options: [
                                      'Death Inside UAE',
                                      'Death Outside UAE',
                                    ],
                                    selectedValue: _reason,
                                    onChanged: (value) {
                                      setState(() {
                                        _reason = value;
                                      });
                                      Future.microtask(() => validateForm());
                                    },
                                    hintText: 'Select a reason',
                                    hintStyle: const TextStyle(
                                      fontFamily: 'Open Sans',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      height: 43 / 14,
                                      color: AppColors.lightGreyColor2,
                                    ),
                                    decoration: InputDecoration(
                                      fillColor: AppColors.whiteColor,
                                      filled: true,
                                      contentPadding: EdgeInsets.symmetric(
                                        vertical: 14,
                                        horizontal: 12,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                          color: AppColors.lightGreyColor2,
                                          width: 1,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                          color: AppColors.lightGreyColor2,
                                          width: 1,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                          color: AppColors.lightGreyColor2,
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            const SizedBox(height: 20),

                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 0.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  HeadingText(
                                    text: 'Attachments',
                                    hasAsterisk: leaveTypesRequiringAttachments
                                        .contains(widget.leaveType),
                                  ),
                                  const SizedBox(height: 8),
                                  if (attachmentIDS != null)
                                    Consumer(
                                      builder: (context, ref, child) {
                                        // final absencesUniqID =
                                        //     widget.absence!.personAbsenceEntryId ??
                                        //         '';

                                        // Watch the provider inside the Consumer
                                        final attachmentIDS = ref.watch(
                                          getAbsenceByUniqIDProvider(
                                            absencesUniqID.toString(),
                                          ),
                                        );

                                        return attachmentIDS.when(
                                          data: (ids) {
                                            int length = min(
                                              AbsenceAttachments.length,
                                              ids.length,
                                            );

                                            return Column(
                                              children: [
                                                for (
                                                  int i = 0;
                                                  i < length;
                                                  i += 2
                                                )
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      buildAttachmentContainer(
                                                        AbsenceAttachments[i],
                                                        screenWidth,
                                                        screenHeight,
                                                        true,
                                                        ids[i],
                                                        ids,
                                                      ),
                                                      if (i + 1 < length)
                                                        SizedBox(
                                                          width:
                                                              screenWidth *
                                                              0.03,
                                                        ),
                                                      if (i + 1 < length)
                                                        buildAttachmentContainer(
                                                          AbsenceAttachments[i +
                                                              1],
                                                          screenWidth,
                                                          screenHeight,
                                                          false,
                                                          ids[i + 1],
                                                          ids,
                                                        ),
                                                    ],
                                                  ),
                                              ],
                                            );
                                          },
                                          loading: () => SizedBox.shrink(),
                                          error:
                                              (error, stackTrace) => Text(
                                                "Error loading attachments",
                                              ),
                                        );
                                      },
                                    ),
                                  Column(
                                    children: [
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          AttachmentField(
                                            uploadedFiles: uploadedFiles,
                                            onFilesChanged: _updateFiles,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),

                                  // SizedBox(height: 24),
                                  // DynamicLinkFields(),
                                  // Replace the existing CustomTextFieldWithHeading section in your code with this:
                                  if (widget.leaveType !=
                                      'Maternity leave') ...[
                                    CustomTextFieldWithHeading(
                                      enabled: !isSubmitting,
                                      controller: _commentsController,
                                      Heading:
                                          widget.leaveType ==
                                                      'Bereavement (1st Degree)' ||
                                                  widget.leaveType ==
                                                      'Bereavement (2nd Degree)' ||
                                                  widget.leaveType ==
                                                      'COVID leave' ||
                                                  widget.leaveType ==
                                                      'Emergency leave' ||
                                                  widget.leaveType ==
                                                      'Hajj leave' ||
                                                  widget.leaveType ==
                                                      'Paternity leave' ||
                                                  widget.leaveType ==
                                                      'Sick leave' ||
                                                  widget.leaveType ==
                                                      'Time off in lieu' ||
                                                  widget.leaveType ==
                                                      'Unpaid leave'
                                              ? 'Comments'
                                              : 'Remarks',
                                      hintText:
                                          widget.leaveType ==
                                                      'Bereavement (1st Degree)' ||
                                                  widget.leaveType ==
                                                      'Bereavement (2nd Degree)' ||
                                                  widget.leaveType ==
                                                      'COVID leave' ||
                                                  widget.leaveType ==
                                                      'Emergency leave' ||
                                                  widget.leaveType ==
                                                      'Hajj leave' ||
                                                  widget.leaveType ==
                                                      'Paternity leave' ||
                                                  widget.leaveType ==
                                                      'Sick leave' ||
                                                  widget.leaveType ==
                                                      'Time off in lieu' ||
                                                  widget.leaveType ==
                                                      'Unpaid leave'
                                              ? 'Enter comments'
                                              : 'Enter remarks',
                                      hasAsterisk: false,
                                      maxlines: 3,
                                      fillColor: AppColors.whiteColor,
                                      hintColor: AppColors.lightGreyshade,
                                      hintStyle: AppColors.lightGreyshade,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final isSubmitting = ref.watch(isSubmittingProvider);

                  return SafeArea(
                    child: ElevatedButton(
                      onPressed:
                          isFormValid && !isSubmitting && projectedbalance >= 0
                              ? () {
                                widget.isEdit == true
                                    ? updateLeaveRequest(ref, context)
                                    : submitLeaveRequest(
                                      context,
                                      ref,
                                      widget.isDraft,
                                    );
                              }
                              : null, // Disabled when loading
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 56),
                        backgroundColor:
                            isFormValid && !isSubmitting
                                ? AppColors.viewColor
                                : AppColors.lightGreyColor2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child:
                          isSubmitting
                              ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: AppColors.whiteColor,
                                  strokeWidth: 2,
                                ),
                              )
                              : DmSansText(
                                'Submit request',
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.whiteColor,
                              ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget WidgetEscortLeave(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20.0, 16.0, 20.0, 10.0),
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: ListView(
              children: [
                HeadingText(text: 'Absence Type', hasAsterisk: true),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () {
                    _showLeaveTypesBottomSheet(context);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.lightGreyColor2),
                      borderRadius: BorderRadius.circular(8),
                      color: AppColors.whiteColor,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        OpenSansText(
                          _selectedAbsenceType ?? 'Select absence type',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color:
                              _selectedAbsenceType != null
                                  ? AppColors.viewColor
                                  : AppColors.lightGreyshade,
                        ),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: AppColors.viewColor,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                AbsenceBalanceWidget(absenceType: '', balance: 12),
                SizedBox(height: 24),
                EscortLeave(),
                if (durationInDays > 1)
                  Column(
                    children: [
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Image.asset(
                            'assets/images/Infoicon.png',
                            height: 16.h,
                            width: 16.w,
                          ),
                          SizedBox(width: 5.w),
                          Text(
                            'Leave balance exceeded. Please adjust your dates.',
                            style: const TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.Orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                Column(
                  children: [
                    SizedBox(height: 24.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        HeadingText(text: 'Reason', hasAsterisk: true),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    AbsenceSelector(
                      options: ['Death Inside UAE', 'Death Outside UAE'],
                      selectedValue: _reason,
                      onChanged: (value) {
                        setState(() {
                          _reason = value;
                          validateForm();
                        });
                      },
                      hintText: 'Select a reason',
                      hintStyle: const TextStyle(
                        fontFamily: 'Open Sans',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: 19 / 14,
                        color: AppColors.lightGreyshade,
                      ),
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(
                            color: AppColors.lightGreyshade,
                            width: 1,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(
                            color: AppColors.lightGreyshade,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(
                            color: AppColors.lightGreyshade,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HeadingText(text: 'Attachments'),
                      SizedBox(height: 8),
                      AttachmentField(
                        uploadedFiles: uploadedFiles,
                        onFilesChanged: _updateFiles,
                      ),
                      // SizedBox(height: 24),
                      // HeadingText(text: 'Link'),
                      // const SizedBox(height: 8),
                      // LinkInputField(controller: _pasteLinkController),
                      CustomTextFieldWithHeading(
                        Heading: 'Comments',
                        hintText: 'Enter',
                        controller: _commentsController,
                        hasAsterisk: false,
                        maxlines: 2,
                        fillColor: AppColors.whiteColor,
                        hintColor: AppColors.lightGreyshade,
                        hintStyle: AppColors.lightGreyshade,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

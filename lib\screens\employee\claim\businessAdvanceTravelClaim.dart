import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/claim/travelClaim.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/claim/components/travelClaimWidget.dart';
import 'package:seawork/screens/employee/claim/components/claimtype.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';

// Define the state model
class BuisnessAdvanceTravelRequestState {
  String selectedOption;
  DateTime? selectedDate;
  DateTime? selectedEndDate;
  TextEditingController businessDetailsController;
  List<FileModel> uploadedFiles;
  List<TextEditingController> linkControllers;
  TextEditingController totalExpenseController;
  TextEditingController dateController;
  final TextEditingController startDateController;
  final TextEditingController endDateController;
  final bool showTravelClaimType;
  String? currentClaimType; // Added claim type tracking
  DateTime? startDate; // Add this to track start date separately

  BuisnessAdvanceTravelRequestState({
    this.selectedOption = "Business travel advance request",
    this.selectedDate,
    this.selectedEndDate,
    required this.businessDetailsController,
    required this.uploadedFiles,
    required this.linkControllers,
    required this.totalExpenseController,
    required this.dateController,
    required this.startDateController,
    required this.endDateController,
    this.showTravelClaimType = true,
    this.currentClaimType,
    this.startDate, // Initialize it
  });
}

// Define the provider
final buisnessAdvanceTravelRequestProvider = StateNotifierProvider<
  BuisnessAdvanceTravelRequestNotifier,
  BuisnessAdvanceTravelRequestState
>((ref) {
  return BuisnessAdvanceTravelRequestNotifier();
});

// Define the notifier
class BuisnessAdvanceTravelRequestNotifier
    extends StateNotifier<BuisnessAdvanceTravelRequestState> {
  BuisnessAdvanceTravelRequestNotifier()
    : super(
        BuisnessAdvanceTravelRequestState(
          businessDetailsController: TextEditingController(),
          uploadedFiles: [],
          linkControllers: [],
          totalExpenseController: TextEditingController(),
          dateController: TextEditingController(),
          startDateController: TextEditingController(),
          endDateController: TextEditingController(),
          startDate: null, // Initialize as null
        ),
      ) {
    _addNewLinkField();
    _setupListeners();
  }

  void _setupListeners() {
    // Add listeners to text controllers to update UI when text changes
    state.totalExpenseController.addListener(() {
      // Force state update to trigger UI rebuild
      _triggerStateUpdate();
    });

    state.businessDetailsController.addListener(() {
      // Force state update to trigger UI rebuild
      _triggerStateUpdate();
    });
  }

  void _triggerStateUpdate() {
    // Create a new state object to trigger rebuild
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: state.currentClaimType,
    );
  }

  void initializeClaimType(String? claimType) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: claimType ?? "Business travel advance request",
    );
  }

  void updateClaimType(String claimType) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: claimType,
    );
  }

  void updateStartDate(DateTime date) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: date,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      showTravelClaimType: state.showTravelClaimType,
      currentClaimType: state.currentClaimType,
      startDate: date, // Update the start date
    );
  }

  void updateEndDate(DateTime date) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: date,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      showTravelClaimType: state.showTravelClaimType,
      currentClaimType: state.currentClaimType,
      startDate: state.startDate, // Keep the existing start date
    );
  }

  void updateUploadedFiles(List<FileModel> files) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: files,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: state.currentClaimType,
    );
  }

  void _addNewLinkField() {
    final controller = TextEditingController();
    controller.addListener(() {
      if (controller.text.isNotEmpty &&
          state.linkControllers.last == controller) {
        _addNewLinkField();
      } else if (controller.text.isEmpty) {
        _removeNewestLinkField();
      }
    });

    state = BuisnessAdvanceTravelRequestState(
      selectedOption: state.selectedOption,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: [...state.linkControllers, controller],
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: state.currentClaimType,
    );
  }

  void _removeNewestLinkField() {
    if (state.linkControllers.length > 1) {
      state.linkControllers.last.dispose();
      state = BuisnessAdvanceTravelRequestState(
        selectedOption: state.selectedOption,
        selectedDate: state.selectedDate,
        selectedEndDate: state.selectedEndDate,
        businessDetailsController: state.businessDetailsController,
        uploadedFiles: state.uploadedFiles,
        linkControllers: state.linkControllers..removeLast(),
        totalExpenseController: state.totalExpenseController,
        dateController: state.dateController,
        startDateController: state.startDateController,
        endDateController: state.endDateController,
        currentClaimType: state.currentClaimType,
      );
    }
  }

  void updateSelectedOption(String option) {
    state = BuisnessAdvanceTravelRequestState(
      selectedOption: option,
      selectedDate: state.selectedDate,
      selectedEndDate: state.selectedEndDate,
      businessDetailsController: state.businessDetailsController,
      uploadedFiles: state.uploadedFiles,
      linkControllers: state.linkControllers,
      totalExpenseController: state.totalExpenseController,
      dateController: state.dateController,
      startDateController: state.startDateController,
      endDateController: state.endDateController,
      currentClaimType: state.currentClaimType,
    );
  }

  bool areMandatoryFieldsFilled() {
    return state.selectedDate != null &&
        state.selectedEndDate != null &&
        state.totalExpenseController.text.isNotEmpty &&
        state.businessDetailsController.text.isNotEmpty;
  }

  void submitRequest() async {
    try {
      if (areMandatoryFieldsFilled()) {
        // Print all form data
        print('=== BUSINESS TRAVEL ADVANCE REQUEST SUBMISSION DATA ===');
        print('Claim Type: ${state.currentClaimType}');
        print('Travel Claim Type: ${state.selectedOption}');
        print('Travel Start Date: ${state.selectedDate}');
        print('Travel End Date: ${state.selectedEndDate}');
        print('Total Amount: ${state.totalExpenseController.text}');
        print(
          'Business Travel Details: ${state.businessDetailsController.text}',
        );
        print('Uploaded Files Count: ${state.uploadedFiles.length}');

        if (state.uploadedFiles.isNotEmpty) {
          print('Uploaded Files:');
          for (int i = 0; i < state.uploadedFiles.length; i++) {
            print(
              '  File ${i + 1}: ${state.uploadedFiles[i].name ?? 'Unknown'}',
            );
          }
        }

        print(
          'Links Count: ${state.linkControllers.where((controller) => controller.text.isNotEmpty).length}',
        );
        if (state.linkControllers.any(
          (controller) => controller.text.isNotEmpty,
        )) {
          print('Links:');
          for (int i = 0; i < state.linkControllers.length; i++) {
            if (state.linkControllers[i].text.isNotEmpty) {
              print('  Link ${i + 1}: ${state.linkControllers[i].text}');
            }
          }
        }
        // Add your submission logic here
      } else {
        // Handle case when mandatory fields are not filled
        print('Mandatory fields are not filled');
      }
    } catch (e) {
      // Handle any errors that occur during submission
      print('Error during form submission: $e');
    }
  }

  void resetState() {
    state.businessDetailsController.dispose();
    state.totalExpenseController.dispose();
    state.dateController.dispose();
    state.startDateController.dispose();
    state.endDateController.dispose();
    for (final controller in state.linkControllers) {
      controller.dispose();
    }

    state = BuisnessAdvanceTravelRequestState(
      businessDetailsController: TextEditingController(),
      uploadedFiles: [],
      linkControllers: [],
      totalExpenseController: TextEditingController(),
      dateController: TextEditingController(),
      startDateController: TextEditingController(),
      endDateController: TextEditingController(),
      startDate: null,
      currentClaimType: null,
    );

    _addNewLinkField();
    _setupListeners();
  }

  @override
  void dispose() {
    state.businessDetailsController.dispose();
    state.totalExpenseController.dispose();
    state.dateController.dispose();
    state.startDateController.dispose();
    state.endDateController.dispose();
    for (var controller in state.linkControllers) {
      controller.dispose();
    }
    super.dispose();
  }
}

class BuisnessAdvanceTravelRequest extends ConsumerStatefulWidget {
  final bool showAppBar;
  final bool showTravelClaimType;
  final bool showClaimTypeDropdown;
  final String? initialClaimType; // Added this parameter
  final String? previousRoute; // Added previousRoute parameter

  const BuisnessAdvanceTravelRequest({
    super.key,
    this.showAppBar = true,
    this.showTravelClaimType = true,
    this.showClaimTypeDropdown = false,
    this.initialClaimType, // Added this parameter
    this.previousRoute, // Added previousRoute parameter
  });
  @override
  ConsumerState<BuisnessAdvanceTravelRequest> createState() =>
      _BuisnessAdvanceTravelRequestState();
}

class _BuisnessAdvanceTravelRequestState
    extends ConsumerState<BuisnessAdvanceTravelRequest> {
  bool isBottomSheetOpen = false;

  String _getAppBarTitle() {
    if (widget.previousRoute == 'ClaimTypesScreen') {
      return 'Add claims';
    } else {
      return 'Travel claims';
    }
  }

  // Add method to handle back navigation
  void _handleBackNavigation() {
    ref.read(buisnessAdvanceTravelRequestProvider.notifier).resetState();
    if (widget.showClaimTypeDropdown && widget.previousRoute != 'TravelClaim') {
      Navigator.of(context).pop();
    } else {
      Navigator.of(
        context,
      ).pushReplacement(MaterialPageRoute(builder: (context) => TravelClaim()));
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(buisnessAdvanceTravelRequestProvider);
    final notifier = ref.read(buisnessAdvanceTravelRequestProvider.notifier);

    // Initialize claim type when widget builds
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.currentClaimType == null && widget.initialClaimType != null) {
        notifier.initializeClaimType(widget.initialClaimType);
      }
    });

    void _showTravelClaimTypeBottomSheet() async {
      setState(() => isBottomSheetOpen = true);
      await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return SelectTravelClaimBottomSheet(
            onOptionSelected: (option) {
              notifier.updateSelectedOption(option);
            },
            initialOption: state.selectedOption,
          );
        },
      );
      setState(() => isBottomSheetOpen = false);
    }

    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: _getAppBarTitle(), // Use dynamic title
        showActionIcon: true,
        onBackPressed: _handleBackNavigation, // Use dynamic back navigation
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.showClaimTypeDropdown) ...[
                HeadingText(text: 'Claim type', hasAsterisk: true),
                const SizedBox(height: 8),
                ClaimTypeSelector(
                  initialClaimType:
                      state.currentClaimType ?? widget.initialClaimType,
                  onClaimTypeChanged: (newClaimType) {
                    notifier.updateClaimType(newClaimType);
                  },
                ),
                // const SizedBox(height: 24),
              ],
              if (widget.showTravelClaimType) ...[
                HeadingText(text: 'Travel claim type', hasAsterisk: true),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: _showTravelClaimTypeBottomSheet,
                  child: Container(
                    width: double.infinity,
                    height: 43,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        OpenSansText(
                          state.selectedOption,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.blackColor,
                        ),
                        CustomSvgImage(
                          imageName:
                              isBottomSheetOpen ? "Arrowup" : "ArrowLeft",
                          height: 24,
                          width: 24,
                          color: AppColors.viewColor,
                        ),
                      ],
                    ),
                  ),
                ),
                // const SizedBox(height: 24),
              ],
              const SizedBox(height: 24),
              CustomDatePickerField(
                dateText: 'Travel start date',
                hintText: 'Select travel date',
                controller: state.startDateController,
                onDateSelected: (date) {
                  notifier.updateStartDate(date);
                },
                isStartDate: true,
                allowPastDates: false,
              ),
              const SizedBox(height: 24),
              CustomDatePickerField(
                dateText: 'Travel end date',
                hintText: 'Select travel date',
                controller: state.endDateController,
                onDateSelected: (date) {
                  notifier.updateEndDate(date);
                },
                isStartDate: false,
                startDate: state.startDate, // Pass the selected start date
              ),
              const SizedBox(height: 24),
              CustomTextFieldWithHeading(
                showSpacing: false,
                Heading: 'Total amount',
                hintText: 'Enter total amount',
                hasAsterisk: true,
                maxlines: 1,
                fillColor: AppColors.whiteColor,
                hintColor: AppColors.lightGreyshade,
                hintStyle: AppColors.lightGreyshade,
                controller: state.totalExpenseController, // Added controller
                keyboardType: TextInputType.number,
                validatePositiveNumber: true,
              ),
              const SizedBox(height: 24),
              CustomTextFieldWithHeading(
                showSpacing: false,
                Heading: 'Business travel details',
                hintText: 'Enter business travel details',
                hasAsterisk: true,
                maxlines: 1,
                fillColor: AppColors.whiteColor,
                hintColor: AppColors.lightGreyshade,
                hintStyle: AppColors.lightGreyshade,
                controller: state.businessDetailsController, // Added controller
                maxLength: 500,
              ),
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HeadingText(text: 'Attachments'),
                    SizedBox(height: 8),
                    AttachmentField(
                      uploadedFiles: state.uploadedFiles,
                      onFilesChanged: (List<FileModel> files) {
                        notifier.updateUploadedFiles(files);
                      }, // Fixed callback
                    ),
                    const SizedBox(height: 24),
                    HeadingText(text: 'Links'),
                    const SizedBox(height: 8),
                    LinkInputList(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ElevatedButton(
              onPressed:
                  notifier.areMandatoryFieldsFilled()
                      ? () => notifier.submitRequest()
                      : null,
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 56),
                backgroundColor:
                    notifier.areMandatoryFieldsFilled()
                        ? AppColors.viewColor
                        : AppColors.lightGreyshade,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: DmSansText(
                'Submit request',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.whiteColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SelectTravelClaimBottomSheet extends StatefulWidget {
  final Function(String) onOptionSelected;
  final String initialOption;

  const SelectTravelClaimBottomSheet({
    Key? key,
    required this.onOptionSelected,
    required this.initialOption,
  }) : super(key: key);

  @override
  _SelectTravelClaimBottomSheetState createState() =>
      _SelectTravelClaimBottomSheetState();
}

class _SelectTravelClaimBottomSheetState
    extends State<SelectTravelClaimBottomSheet> {
  late String selectedOption;

  @override
  void initState() {
    super.initState();
    selectedOption = widget.initialOption;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        color: AppColors.whiteColor,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 6.0),
            child: Center(
              child: Container(
                width: 30,
                height: 5,
                decoration: BoxDecoration(
                  color: AppColors.viewColor,
                  borderRadius: BorderRadius.circular(9),
                ),
              ),
            ),
          ),
          TravelClaimOptions(
            initialOption: selectedOption,
            onOptionSelected: (selectedOption) {
              widget.onOptionSelected(
                selectedOption,
              ); // Pass selection back to parent
              Navigator.pop(context); // Close bottom sheet
            },
          ),
        ],
      ),
    );
  }
}

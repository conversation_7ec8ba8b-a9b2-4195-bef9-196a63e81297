import 'package:go_router/go_router.dart';
import 'package:seawork/components/form/bottomSheet.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/form/searchInput.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/list/renderItem.dart';
import 'package:seawork/components/scroll/scrollPaginationContainer.dart';
import 'package:seawork/components/text/dsSansText.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/service/ticket/models/ticketType.dart';
import 'package:seawork/screens/service/ticket/providers/ticketProviders.dart';
import 'package:seawork/screens/employee/absence/models/status.dart';
import 'package:seawork/screens/service/ticket/helpHelper.dart';

// Main Help Class - changed to StatefulWidget for scroll controller
class Help extends ConsumerStatefulWidget {
  final String iconClicked;

  static String tag = 'help';

  const Help({Key? key, required this.iconClicked}) : super(key: key);

  @override
  ConsumerState<Help> createState() => _HelpState();
}

var requestTypes = ['Request a service', 'Report an incident'];

class _HelpState extends ConsumerState<Help> {
  // Add scroll controller for pagination
  final ticketFilterProvider = StateProvider<String>((ref) => 'All tickets');

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget buildTicketsList(
    BuildContext context,
    List tickets,
    String selectedFilter,
    AsyncValue<List<TicketType>> ticketTypesAsync,
    AsyncValue<List<Status>> statusesAsync,
  ) {
    final filteredTickets =
        tickets.where((ticket) {
          final status = getStatusFromId(ticket.statusId ?? 1);
          switch (selectedFilter) {
            case 'Open tickets':
              return status == 'Open ticket';
            case 'Closed tickets':
              return status == 'Closed ticket';
            case 'Awaiting tickets':
              return status == 'In Progress';
            default: // 'All tickets'
              return true;
          }
        }).toList();

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: filteredTickets.length,
      itemBuilder: (context, index) {
        return buildScrollableTabContent(
          context,
          filteredTickets[index],
          ticketTypesAsync,
          statusesAsync,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final iconClicked = widget.iconClicked;
    final selectedFilter = ref.watch(ticketFilterProvider);
    // Watch pagination state instead of ticketsProvider
    final paginationState = ref.watch(ticketPaginationProvider);
    final ticketTypesAsync = ref.watch(ticketTypesProvider);
    final statusesAsync = ref.watch(statusProvider);

    return PopScope(
              canPop: true,
              onPopInvoked: (didPop) {
                if (!didPop) {
                  context.pop();
                }
              },
              child:
    SecondaryScaffoldWithAppBar(
      context,
      iconClicked,
      SvgImage24x24('assets/images/appbackbutton.svg'),
      () {
        context.pop();
      },
      bodyItem: ScrollPaginationContainer(
        onRefresh: () async {
          await ref.read(ticketPaginationProvider.notifier).refreshTickets();
          //await ref.refresh(ticketTypesProvider.future);
          //await ref.refresh(statusProvider.future);
        },
        onLoadMore: () async {
          await ref.read(ticketPaginationProvider.notifier).loadMoreTickets();
        },
        isLoading: paginationState.isLoading,
        hasMore: paginationState.hasMore,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          Container(
            margin: const EdgeInsets.only(top: 50.0),
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: GoogleFonts.openSans(
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
                children: const [
                  TextSpan(text: 'How can we assist you\n'),
                  TextSpan(text: 'today?'),
                ],
              ),
            ),
          ),
          SearchInput(
            context,
            onTap: () {
              context.push('/search-tickets');
            },
            hintText: 'Search services, article and tickets',
            suffixIcon: CustomSvgImage(imageName: "search_icon"),
          ),
          const SizedBox(height: 20),

          for (var requestType in requestTypes)
            RenderListItemWithTextAndIcon(
              context,
              requestType,
              CustomSvgImage(
                imageName:
                    requestType == "Request a service"
                        ? "Requestaservice"
                        : "Reportanincident",
              ),
              requestType,
              (requestType) => {
                context.push(
                  '/ticket-category-selection',
                  extra: {
                    'iconClicked': requestType,
                    'itilTicketTypeId':
                        requestType == "Request a service" ? 3 : 1,
                  },
                ),
              },
            ),
          Padding(
            padding: const EdgeInsets.only(left: 12.0, top: 24.0, bottom: 12),
            child: Align(
              alignment: Alignment.centerLeft,
              child: DMSans600Medium(14, 'My tickets', AppColors.blackColor),
            ),
          ),

          // buildFilterButton(context, selectedFilter),
          SafeArea(
            child: BottomSheetDropdown(
              selectedFilter: selectedFilter,
              ref: ref,
              onFilterSelected: (ticketFilter) {
                ref.read(ticketFilterProvider.notifier).state = ticketFilter;
              },
              filterOptions: const [
                'All tickets',
                'Open tickets',
                'Closed tickets',
                'Awaiting tickets',
              ],
            ),
          ),
          SizedBox(height: 8),
          if (paginationState.tickets.isNotEmpty)
            buildTicketsList(
              context,
              paginationState.tickets,
              selectedFilter,
              ticketTypesAsync,
              statusesAsync,
            ),
          if (paginationState.error != null)
            Center(
              child: Text('Error loading tickets: ${paginationState.error}'),
            ),
          if (paginationState.tickets.isEmpty && !paginationState.isLoading)
            Center(
              child: DMSans600Large(
                16,
                'No tickets found',
                AppColors.headingColor,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
     ) );
  }
}

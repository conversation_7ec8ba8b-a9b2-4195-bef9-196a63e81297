import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/account/profile/components/ageCalcultor.dart';
import 'package:seawork/screens/account/profile/components/customProfilePictureWidget.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/screens/account/profile/components/personInfoWidget.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/screens/account/profile/repository/profileImageRepository.dart';
import 'package:seawork/screens/account/profile/repository/profileImageEditRepositorty.dart';
import 'package:seawork/screens/account/profile/repository/profileImageProvidor.dart';
import 'package:seawork/utils/style/colors.dart';

class PersonalInfoDetail extends ConsumerStatefulWidget {
  final Item? firstItem;
  final String? selectedFieldDemographic;
  final Function(String) onFieldTapped;
  final bool isLoading;

  const PersonalInfoDetail({
    Key? key,
    required this.firstItem,
    required this.selectedFieldDemographic,
    required this.onFieldTapped,
    required this.isLoading,
  }) : super(key: key);

  @override
  ConsumerState<PersonalInfoDetail> createState() => _PersonalInfoDetailState();
}

class _PersonalInfoDetailState extends ConsumerState<PersonalInfoDetail> {
  Uint8List? profileImage;
  bool isImageLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  Future<void> _loadProfileImage() async {
    setState(() => isImageLoading = true);
    try {
      final imageRepo = ref.read(imageRepositoryProvider);
      final imageBytes = await imageRepo.fetchImageBytes();
      setState(() => profileImage = imageBytes);
    } catch (e) {
      debugPrint('Error loading profile image: $e');
    } finally {
      setState(() => isImageLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          ProfileStackWidget(
            showEditButton: true,

            imageWidget:
                profileImage != null
                    ? Image.memory(
                      profileImage!,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (context, error, stackTrace) => CustomPngImage(
                            imageName: 'noprofile',
                            height: 89,
                            width: 89,
                          ),
                    )
                    : ref
                        .watch(imageBytesProvider)
                        .when(
                          data:
                              (bytes) => Image.memory(
                                bytes,
                                fit: BoxFit.cover,
                                errorBuilder:
                                    (context, error, stackTrace) =>
                                        CustomPngImage(
                                          imageName: 'noprofile',
                                          height: 89,
                                          width: 89,
                                        ),
                              ),
                          loading:
                              () => CustomPngImage(
                                imageName: 'noprofile',
                                height: 89,
                                width: 89,
                              ),
                          error:
                              (e, _) => CustomPngImage(
                                imageName: 'noprofile',
                                height: 89,
                                width: 89,
                              ),
                        ),
            // profileImage != null
            //     ? Image.memory(
            //       profileImage!,
            //       fit: BoxFit.cover,
            //       errorBuilder:
            //           (context, error, stackTrace) => CustomPngImage(
            //             imageName: 'noprofile',
            //             height: 89,
            //             width: 89,
            //           ),
            //     )
            //     : CustomPngImage(
            //       imageName: 'noprofile',
            //       height: 89,
            //       width: 89,
            //     ),
            onImageSelected: (newImageBytes) async {
              setState(() {
                profileImage = newImageBytes;
              });
              try {} catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error processing image: $e')),
                );
              }
            },
          ),

          const SizedBox(height: 24),
          Expanded(
            child: ListView(
              children: [
                _buildNameCard(),
                _buildDemographicInfoCard(),
                _buildBiographicalInfoCard(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(0),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowBoxColor.withOpacity(0.25),
            blurRadius: 9.6,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: CardSectionTwo(
        title: "Name",
        subtitle: "View name details",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, bottom: 12, top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                OpenSansText(
                  "Global name",
                  textAlign: TextAlign.center,
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
          SelectableRow(
            label: "Title",
            value: "${widget.firstItem?.salutation}",
            isSelected: widget.selectedFieldDemographic == 'Title',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "First name",
            value: "${widget.firstItem?.firstName}",
            isSelected: widget.selectedFieldDemographic == 'First name',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Middle name",
            value: "${widget.firstItem?.middleName}",
            isSelected: widget.selectedFieldDemographic == 'Middle name',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Last name",
            value: "${widget.firstItem?.lastName}",
            isSelected: widget.selectedFieldDemographic == 'Last name',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Full name as per passport",
            value: "${widget.firstItem?.displayName}",
            isSelected:
                widget.selectedFieldDemographic == 'Full name as per passport',
            onTap: widget.onFieldTapped,
          ),
        ],
      ),
    );
  }

  Widget _buildDemographicInfoCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowBoxColor.withOpacity(0.25),
            blurRadius: 9.6,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: CardSectionTwo(
        title: "Demographic info",
        subtitle: "View demographic info",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          SelectableRow(
            label: "Country",
            value: "${widget.firstItem?.country ?? "----"}",
            isSelected: widget.selectedFieldDemographic == "Country",
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Religion",
            value: "${widget.firstItem?.religion}",
            isSelected: widget.selectedFieldDemographic == 'Religion',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Gender",
            value:
                widget.firstItem?.gender == 'M'
                    ? 'Male'
                    : widget.firstItem?.gender == 'F'
                    ? 'Female'
                    : "${widget.firstItem?.gender}",
            isSelected: widget.selectedFieldDemographic == 'Gender',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Marital status",
            value:
                widget.firstItem?.maritalStatus == 'M'
                    ? 'Married'
                    : widget.firstItem?.maritalStatus == 'D'
                    ? 'Divorced'
                    : widget.firstItem?.maritalStatus == 'W'
                    ? 'Widowed'
                    : widget.firstItem?.maritalStatus == 'S'
                    ? 'Single'
                    : "${widget.firstItem?.maritalStatus}",
            isSelected: widget.selectedFieldDemographic == 'Marital status',
            onTap: widget.onFieldTapped,
          ),
          SelectableRow(
            label: "Highest level of education",
            value: "----",
            isSelected:
                widget.selectedFieldDemographic == 'Highest level of education',
            onTap: widget.onFieldTapped,
          ),
        ],
      ),
    );
  }

  Widget _buildBiographicalInfoCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowBoxColor.withOpacity(0.25),
            blurRadius: 9.6,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: CardSectionTwo(
        title: "Biographical info",
        subtitle: "View biographical info",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          SelectableRow(
            label: "Date of birth",
            value:
                widget.firstItem?.dateOfBirth != null
                    ? DateFormat(
                      'dd- MMM- yyyy',
                    ).format(widget.firstItem!.dateOfBirth!)
                    : "N/A",
            isSelected: widget.selectedFieldDemographic == 'Date of birth',
            onTap: widget.onFieldTapped,
          ),
          CalculateAge(
            dateOfBirth: widget.firstItem?.dateOfBirth,
            isSelected: widget.selectedFieldDemographic == 'Age',
            onTap: () {},
          ),
        ],
      ),
    );
  }
}

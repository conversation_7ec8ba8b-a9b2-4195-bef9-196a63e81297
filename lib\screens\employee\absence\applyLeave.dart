import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/scroll/scrollPaginationContainer.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/customLoaderWithMessages.dart';
import 'package:seawork/components/widget/customMainTab.dart';
import 'package:seawork/components/widget/customStatusTabbar.dart';
import 'package:seawork/components/widget/customTabBarCard.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/employee/absence/components/customCreatedByMeLeaveTabBar.dart';
import 'package:seawork/screens/employee/absence/components/leaveSummaryCard.dart';
import 'package:seawork/screens/employee/absence/components/leaveTypeSearchBar.dart';
import 'package:seawork/screens/employee/absence/generateAndSavePDF.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/planBalanceSummaryResponse.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absenceTaskListNotifer.dart';
import 'package:seawork/screens/employee/absence/providers/absencesNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/employee/absence/selectLeaveTypeScreen.dart';
import 'package:seawork/components/widget/customCompactCalendar.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/style/sizeConfig.dart';
import 'package:seawork/utils/util.dart';

class ApplyLeave extends ConsumerStatefulWidget {
  const ApplyLeave({super.key});

  @override
  ConsumerState<ApplyLeave> createState() => _ApplyLeaveState();
}

class _ApplyLeaveState extends ConsumerState<ApplyLeave> {
  int _selectedTabIndex = 0;
  Map<String, dynamic>? _selectedLeaveType;
  late FocusNode searchFocusNode;
  bool showSearchField = false;
  TextEditingController searchController = TextEditingController();
  final initialFetchDoneProvider = StateProvider<bool>((ref) => false);
  bool noMatchFound = false;
  bool noMatchForTasks = false;

  Map<String, dynamic>? employeeDetails;
  String? personNumber;
  String? gender;
  String? religion;
  List<dynamic> roles = [];
  bool noMatchAwaiting = false;
  bool noMatchApproved = false;
  bool noMatchDenied = false;
  bool noMatchWithdrawn = false;
  bool noMatchDraft = false;
  bool _initialLoadingComplete = false;
  @override
  void initState() {
    super.initState();
    searchFocusNode = FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      
      refreshLeavePageState();
    });
     
  }

  Future<void> refreshLeavePageState() async {
    if (!mounted) return;

    try {
      await _loadEmployeeDetails();
      if (!mounted) return;

      final query = ref.read(taskSearchQueryProvider);

      ref.invalidate(pendingTasksProvider(query));
      ref.invalidate(preloadAssignedToMeTasksProvider);
      ref.invalidate(combinedLeaveInitProvider);

      if (!mounted) return;

      ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      ref.read(isSubmittingProvider.notifier).state = false;
    } catch (e) {
      print("Error in refreshLeavePageState: $e");
    }

    ref.listen<Map<String, int>>(taskCountsProvider, (previous, next) {
      final hasPending = (next['Awaiting'] ?? 0) > 0;
      final current = ref.read(hasPendingTasksProvider);
      if (hasPending && !current) {
        ref.read(hasEverHadPendingTasksProvider.notifier).state = true;
      }
    });
  }

  Future<void> _loadEmployeeDetails() async {
    try {
      final jsonString = await PreferencesUtils.getEmployeeDetails();
      if (jsonString == null) return;

      final json = jsonDecode(jsonString);
      if (!mounted) return;
      setState(() {
        employeeDetails = json;
        personNumber = json['PersonNumber'];
        gender = json['Gender'];
        religion = json['Religion'];
        roles = json['roles'] ?? [];
      });

      // Optional: only if you still want to store subfields separately
      await PreferencesUtils.setEmployeeSubFields(json);
    } catch (e) {}
  }

   fetchAllAbsences() async {
    noMatchFound = false;
    ref.read(offsetProvider.notifier).state = 0;
    ref.read(awaitingListNotifierProvider.notifier).fetchData();
    ref.read(approvedListNotifierProvider.notifier).fetchData();
    ref.read(deniedListNotifierProvider.notifier).fetchData();
    ref.read(withdrawnListNotifierProvider.notifier).fetchData();
    ref.read(draftListNotifierProvider.notifier).fetchData();
  }

  Future<void> refreshAllProvidersWithAwait() async {
  setState(() {}); // triggers rebuild so `.when(loading:)` can kick in
  final query = ref.read(taskSearchQueryProvider);
  await ref.refresh(pendingTasksProvider(query).future);
  await ref.refresh(preloadAssignedToMeTasksProvider.future);
  await ref.refresh(combinedLeaveInitProvider.future);
  await fetchAllAbsences();
}


  void fetchAllTask()async {
setState(() {
  noMatchForTasks = false;
});   
        ref.read(pendingAbsenceTasksNotifierProvider.notifier).clearSearch();

  }

  void searchForLeaveType(Map<String, dynamic> selectedMap) async {
    ref.read(selectedSearchLeaveTypeProvider.notifier).state = selectedMap;
    final awaitingResult =
        await ref
            .read(awaitingListNotifierProvider.notifier)
            .fetchSearchAbsence();
    final approvedResult =
        await ref
            .read(approvedListNotifierProvider.notifier)
            .fetchSearchAbsence();
    final deniedResult =
        await ref
            .read(deniedListNotifierProvider.notifier)
            .fetchSearchAbsence();
    final withdrawnResult =
        await ref
            .read(withdrawnListNotifierProvider.notifier)
            .fetchSearchAbsence();
    final draftResult =
        await ref.read(draftListNotifierProvider.notifier).fetchSearchAbsence();

    noMatchAwaiting = !awaitingResult;
    noMatchApproved = !approvedResult;
    noMatchDenied = !deniedResult;
    noMatchWithdrawn = !withdrawnResult;
    noMatchDraft = !draftResult;

    // This tells whether **all** tabs had no data matching search
    noMatchFound =
        !awaitingResult &&
        !approvedResult &&
        !deniedResult &&
        !withdrawnResult &&
        !draftResult;

    setState(() {});
  }

  void searchForTask(Map<String, dynamic> selectedMap) {
    setState(() {
      noMatchForTasks = false;
    });
    if (selectedMap.containsKey('query')) {
      final String query = selectedMap['query']?.toString().trim() ?? '';
        ref.read(pendingAbsenceTasksNotifierProvider.notifier).setSearchQuery(query);
    }
  }

  void preloadAssignedToMeTasks() {
    try {
      final query = ref.read(taskSearchQueryProvider);
      ref.read(pendingTasksProvider(query));
      // ref.read(approvedTasksProvider(query));
      // ref.read(rejectedTasksProvider(query));
      // ref.read(withdrawnTasksProvider(query));
    } catch (e) {}
  }

  void onSearchNoMatchFound() {
    setState(() {
      noMatchFound = true;
      noMatchForTasks = true;
    });
  }

  @override
  void dispose() {
    searchFocusNode.dispose();
    searchController.dispose();
    noMatchFound = false;
    noMatchForTasks = false;
    super.dispose();
  }

  bool hasFetchedCounts = false;

  void _handleSystemBack() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);
    final hasPendingTasks = ref.watch(hasPendingTasksProvider);
    final leaveTypeAsync = ref.watch(combinedLeaveInitProvider);
    final leaveTypesRepo = ref.watch(leaveTypesRepositoryProvider);
    final awaitingCount = leaveTypesRepo.totalStatusCounts["AWAITING"] ?? 0;
    final approvedCount = leaveTypesRepo.totalStatusCounts["APPROVED"] ?? 0;
    final rejectedCount = leaveTypesRepo.totalStatusCounts["DENIED"] ?? 0;
    final withdrawnCount =
        leaveTypesRepo.totalStatusCounts["ORA_WITHDRAWN"] ?? 0;
    final totalLeaveCount =
        awaitingCount + approvedCount + rejectedCount + withdrawnCount;
    bool _shouldUpdateSelectedLeaveType = true;
    final searchQuery = ref.watch(taskSearchQueryProvider);

    ref.watch(awaitingListNotifierProvider);
    ref.watch(initialFetchDoneProvider);
    bool isLoading = leaveTypeAsync.isLoading;
    String? personPlanEnrollmentId = ref.watch(personPlanEnrollmentIdProvider);
    final planBalancesAsync = ref.watch(getPlanBalanceProvider);

    final AsyncValue<List<PlanBalanceSummaryItemModel>>
    planBalanceSummaryAsync =
        personPlanEnrollmentId != null &&
                personPlanEnrollmentId.isNotEmpty &&
                personPlanEnrollmentId != "0"
            ? ref.watch(getPlanBalanceSummaryProvider(personPlanEnrollmentId))
            : const AsyncValue<List<PlanBalanceSummaryItemModel>>.data([]);

    final leaveTypeName =
        _selectedLeaveType != null
            ? _selectedLeaveType!['name']
            : 'Annual Leave';

    void handlePlanBalances(
      AsyncValue<List<PlanBalanceModel>> planBalancesAsync,
      String leaveType,
      Function(double?) onBalanceUpdated,
      Function(String?) onPersonPlanEnrollmentIdFound,
    ) {
      planBalancesAsync.when(
        data: (planBalances) {
          if (planBalances.isEmpty) {
            onBalanceUpdated(-1);
            return;
          }

          final hasMatchingPlan = planBalances.any(
            (plan) => plan.planName == leaveType,
          );

          if (!hasMatchingPlan) {
            onBalanceUpdated(0);
            onPersonPlanEnrollmentIdFound("0");
            return;
          }

          final matchingPlan = planBalances.firstWhere(
            (plan) => plan.planName == leaveType,
          );

          final selectedBalance =
              matchingPlan.balanceAsOfBalanceCalculationDate;
          final absenceBalance =
              selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

          onBalanceUpdated(absenceBalance as double?);
          onPersonPlanEnrollmentIdFound(
            matchingPlan.personPlanEnrollmentId.toString(),
          );
        },
        loading: () => Container(),
        error: (err, stack) {
          onBalanceUpdated(-1);
        },
      );
    }

    handlePlanBalances(
      planBalancesAsync,
      leaveTypeName,
      (balance) {
        setState(() {
          if (!mounted) return;
        });
      },
      (foundId) {
        if (!mounted) return;
        if (foundId != null) {
          Future.microtask(() {
            if (!mounted) return;
            ref.read(personPlanEnrollmentIdProvider.notifier).state = foundId;
          });
        }
      },
    );

    double totalLeaves = 0;
    double leaveBalance = 0;
    double leaveTaken = 0;
    if (personPlanEnrollmentId != null) {
      planBalanceSummaryAsync.when(
        data: (planBalanceSummary) {
          Future.microtask(() {
            if (!mounted) return;
            ref
                .read(totalLeavesProvider.notifier)
                .updateTotalLeaves(planBalanceSummary);
            ref
                .read(leaveBalanceProvider.notifier)
                .updateLeaveBalance(planBalanceSummary);
            ref
                .read(leaveTakenProvider.notifier)
                .updateLeaveTaken(planBalanceSummary);
          });

          totalLeaves = ref.watch(totalLeavesProvider);
          leaveBalance = ref.watch(leaveBalanceProvider);
          leaveTaken = ref.watch(leaveTakenProvider);
        },
        loading: () {},
        error: (err, _) {},
      );
    }

    List<Map<String, dynamic>> leaveTypesData = [];

    leaveTypeAsync.when(
      data: (data) {
        leaveTypesData =
            data.leaveTypes
                .where((e) => e['name'] != null && e['id'] != null)
                .where((e) {
                  final name = (e['name'] as String).toLowerCase();

                  if (name.contains('maternity') &&
                      gender?.toLowerCase() == 'f') {
                    return false;
                  }

                  if (name.contains('paternity') &&
                      gender?.toLowerCase() == 'm') {
                    return false;
                  }

                  if (name.contains('hajj') &&
                      religion?.toLowerCase() == 'muslim') {
                    return false;
                  }

                  return true;
                })
                .toList();

        if (leaveTypesData.isNotEmpty && _selectedLeaveType == null) {
          _selectedLeaveType = leaveTypesData.first;
        }
      },
      loading: () => '',
      error: (e, _) => '',
    );

    List<Map<String, dynamic>> leaveTypes = leaveTypesData;

    if (leaveTypes.isNotEmpty) {
      Future.microtask(() {
        if (!mounted) return;
        ref.read(initialFetchDoneProvider.notifier).state = true;
        ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      });
    }

    List<String> leaveTypeNames =
        leaveTypes
            .map((e) => capitalizeFirstWordOnly(e['name'].toString()))
            .toList();

    void selectLeaveType(Map<String, dynamic> selectedMap) {
      setState(() {
        if (!mounted) return;
        _selectedLeaveType = selectedMap;
      });

      final selectedAbsenceTypeId = _selectedLeaveType!['id'];
      if (selectedAbsenceTypeId == null) return;

      ref.invalidate(
        getAbsenceProvider(
          AbsenceQueryParams(
            offset: 0,
            absenceTypeId: selectedAbsenceTypeId.toString(),
          ),
        ),
      );

      handlePlanBalances(
        planBalancesAsync,
        leaveTypeName,
        (balance) {
          setState(() {
            if (!mounted) return;
          });
        },
        (foundId) {
          if (foundId != null) {
            Future.microtask(() {
              if (!mounted) return;
              ref.read(personPlanEnrollmentIdProvider.notifier).state = foundId;
            });
          }
        },
      );

      if (_shouldUpdateSelectedLeaveType) {
        ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      }
    }

    Future<void> fetchTaskCountsForAllStatuses(WidgetRef ref) async {
      if (!mounted) return;
      final repo = ref.read(leaveTypesRepositoryProvider);

      final statuses = {
        'Awaiting': 'ASSIGNED',
        'Approved': 'APPROVED',
        'Rejected': 'REJECTED',
        'Withdrawn': 'WITHDRAWN',
      };

      for (var entry in statuses.entries) {
        try {
          final response = await repo.getTasksForAssignToMe(
            status: entry.value,
          );
          ref
              .read(taskCountsProvider.notifier)
              .setCount(entry.key, response.totalResults ?? 0);
        } catch (e) {}
      }
    }

    final CreatedByMetabs = [
      StatusTab(label: 'Pending'),
      StatusTab(label: 'Approved', count: approvedCount),
      StatusTab(label: 'Rejected', count: rejectedCount),
      StatusTab(label: 'Withdrawn', count: withdrawnCount),
      StatusTab(label: 'Draft'),
    ];
    // Trigger fetch logic when second tab is selected
    if (_selectedTabIndex == 1 && !hasFetchedCounts) {
      hasFetchedCounts = true;
      Future.microtask(() {
        if (!mounted) return;
        fetchTaskCountsForAllStatuses(ref);
      });
    }

    final taskCounts = ref.watch(taskCountsProvider);
    final taskTotalCounts = taskCounts.values.reduce((a, b) => a + b);

    final AssignToMetabs = [
      StatusTab(label: 'Pending', count: taskCounts['Awaiting'] ?? 0),
      // StatusTab(label: 'Approved'),
      // StatusTab(label: 'Rejected'),
      // StatusTab(label: 'Withdrawn', count: taskCounts['Withdrawn'] ?? 0),
    ];

     final isAwaitingLoading = ref.watch(awaitingListNotifierProvider).isLoading;
    final isApprovedLoading = ref.watch(approvedListNotifierProvider).isLoading;
    final isDeniedLoading = ref.watch(deniedListNotifierProvider).isLoading;
    final isWithdrawnLoading =
        ref.watch(withdrawnListNotifierProvider).isLoading;
    final isDraftLoading = ref.watch(draftListNotifierProvider).isLoading;
    // final isPendingTasksLoading =
    //     ref.watch(pendingTasksProvider(searchQuery)).isLoading;
    // final isApprovedTasksLoading = ref.watch(approvedTasksProvider).isLoading;
    // final isRejectedTasksLoading = ref.watch(rejectedTasksProvider).isLoading;
    // final isWithdrawnTasksLoading = ref.watch(withdrawnTasksProvider).isLoading;
    final isLoadingTabsData =
        isAwaitingLoading ||
        isApprovedLoading ||
        isDeniedLoading ||
        isWithdrawnLoading ||
        isDraftLoading;
    // isPendingTasksLoading;
    //     isApprovedTasksLoading ||
    //     isRejectedTasksLoading ||
    //     isWithdrawnTasksLoading;

    return Stack(
      children: [
      PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }
        context.pop();
      },
          child: PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:Scaffold(
            extendBody: true,
            backgroundColor: AppColors.secondaryColor,
            appBar: CustomAppBar(
              title: 'Apply leave',
              showActionIcon: true,
            ),
            body: leaveTypeAsync.when(
              loading: () => Center(child: RotatingLoaderWithMessages()),
              error: (err, stack) => Center(child: OpenSansText("Error: $err")),
              data: (initData) {
                 if (!_initialLoadingComplete) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      setState(() {
                        _initialLoadingComplete = true;
                      });
                    }
                  });
                }

                return ScrollPaginationContainer(
                   onRefresh: refreshAllProvidersWithAwait,
        onLoadMore: () async {}, // optional for future pagination
        isLoading: false, // or use any loading state if present
        hasMore: false,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(18),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            LeaveSummaryCard(
                              leaveTypes: leaveTypes,
                              onUpdateLeaveTypeFlag: (bool value) {
                                setState(() {
                                  _shouldUpdateSelectedLeaveType = value;
                                });
                              },
                              isLoading: isLoading,
                              leaveTypeNames: leaveTypeNames,
                              totalLeaves: totalLeaves,
                              leaveBalance: leaveBalance,
                              leaveTaken: leaveTaken,
                              selectLeaveType: selectLeaveType,
                              generateAndSavePDF: generateAndSavePDF,
                            ),
                             SizedBox(height: 20.h),
                                if (hasPendingTasks)
                            CustomMainTabBar(
                              selectedIndex: _selectedTabIndex,
                              onTabSelected: (index) {
                                setState(() {
                                  _selectedTabIndex = index;
                                });
                              },
                              tabs: [
                                TabItem(
                                  label: 'Created by me',
                                  badgeCount: totalLeaveCount,
                                ),
                                  TabItem(
                                    label: 'Assigned to me',
                                    badgeCount: taskTotalCounts,
                                  ),
                              ],
                            ),
                             SizedBox(height: 20.h),
                            if (_selectedTabIndex == 0)
                              DefaultTabController(
                                length: 5,
                                child: Builder(
                                  builder: (context) {
                                    TabController tabController =
                                        DefaultTabController.of(context);
                                    return Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(left: 6.0),
                                          child: CustomStatusTabBar(
                                            controller: tabController,
                                            tabs: CreatedByMetabs,
                                          ),
                                        ),
                                         SizedBox(height: 12.h),
                                        LeaveTypeSearchRow(
                                          tabIndex: _selectedTabIndex,
                                          showSearchField: showSearchField,
                                          searchController: searchController,
                                          leaveTypes: leaveTypes,
                                          onSearchNoMatchFound: onSearchNoMatchFound,
                                          searchForLeaveType: searchForLeaveType,
                                          fetchAllAbsences: fetchAllAbsences,
                                          toggleSearch: () {
                                            setState(() {
                                              searchController.clear();
                                              showSearchField = !showSearchField;
                                              if (showSearchField) {
                                                Future.delayed(
                                                  Duration(milliseconds: 100),
                                                  () {
                                                    searchFocusNode.requestFocus();
                                                  },
                                                );
                                              }
                                            });
                                          },
                                          filterWidget: FilterBasedOnDates(
                                            selectedLeaveType: _selectedLeaveType,
                                            dateRangeProvider:
                                                applyLeaveDateRangeProvider,
                                          ),
                                          searchFocusNode: searchFocusNode,
                                        ),
                                        Consumer(
                                          builder: (context, ref, child) {
                                            // final dateRange = ref.watch(
                                            //   dateRangeProvider,
                                            // );
                                            final dateRange = ref.watch(
                                              applyLeaveDateRangeProvider,
                                            );
                                                            
                                            if (dateRange == null ||
                                                dateRange['startDate'] == null ||
                                                dateRange['endDate'] == null) {
                                              return  SizedBox(height: 6.h,);
                                            }
                                                            
                                            final startDate = DateFormat(
                                              'dd MMM yyyy',
                                            ).format(dateRange['startDate']!);
                                            final endDate = DateFormat(
                                              'dd MMM yyyy',
                                            ).format(dateRange['endDate']!);
                                                            
                                            return Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                // Date range on the left
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 10),
                                                  child: OpenSansText(
                                                    '$startDate - $endDate',
                                                    fontSize: 14,
                                                    color: AppColors.viewColor,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                                          
                                                // Clear filters button on the right
                                                TextButton(
                                                  onPressed: () {
                                                    // ref
                                                    //     .read(
                                                    //       dateRangeProvider.notifier,
                                                    //     )
                                                    //     .state = null;
                                                    ref
                                                        .read(
                                                          applyLeaveDateRangeProvider
                                                              .notifier,
                                                        )
                                                        .state = null;
                                                          
                                                    fetchAllAbsences();
                                                  },
                                                  child: OpenSansText(
                                                    'clear filters',
                                                    fontSize: 12,
                                                    color: AppColors.viewColor,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                    
                                        noMatchFound
                                            ? Padding(
                                              padding: const EdgeInsets.only(top: 10),
                                              child: noSearchResultWidget(context),
                                            )
                                            : Container(
                                              height: 400.h,
                                              child: TabBarView(
                                                children: [
                                                  ref
                                                      .watch(
                                                        awaitingListNotifierProvider,
                                                      )
                                                      .when(
                                                        data: (awaitingAbsences) {
                                                          return awaitingAbsences
                                                                  .isNotEmpty
                                                              ? CreatedByMeLeaveTabBarWidget(
                                                                leaveTypes:
                                                                    leaveTypeNames,
                                                                absenceData:
                                                                    awaitingAbsences,
                                                                status: 'pending',
                                                                iconClicked:
                                                                    'Apply Leave',
                                                                selectedTabIndex:
                                                                    _selectedTabIndex,
                                                              )
                                                              : (noMatchAwaiting
                          ? noSearchResultWidget(context)
                          : noDataFoundWidget(context));
                                                              
                                                        },
                                                        loading:
                                                            () => Container(),
                                                        error:
                                                            (err, stack) =>
                                                               noMatchFound ? noSearchResultWidget(context) : noDataFoundWidget(
                                                                  context,
                                                                ),
                                                      ),
                                                  ref
                                                      .watch(
                                                        approvedListNotifierProvider,
                                                      )
                                                      .when(
                                                        data: (approvedAbsences) {
                                                          if (!mounted)
                                                            return const SizedBox.shrink();
                                                          return approvedAbsences
                                                                  .isNotEmpty
                                                              ? CreatedByMeLeaveTabBarWidget(
                                                                leaveTypes:
                                                                    leaveTypeNames,
                                                                absenceData:
                                                                    approvedAbsences ??
                                                                    [],
                                                                status: 'approved',
                                                                iconClicked:
                                                                    'Apply Leave',
                                                                selectedTabIndex:
                                                                    _selectedTabIndex,
                                                              )
                                                              : (noMatchApproved
                          ? noSearchResultWidget(context)
                          : noDataFoundWidget(context));
                                                        },
                                                        loading:
                                                            () => Container(),
                                                        error:
                                                            (err, stack) =>
                                                               noMatchFound ? noSearchResultWidget(context) : noDataFoundWidget(
                                                                  context,
                                                                ),
                                                      ),
                                                  ref
                                                      .watch(deniedListNotifierProvider)
                                                      .when(
                                                        data: (deniedAbsences) {
                                                          return deniedAbsences
                                                                  .isNotEmpty
                                                              ? CreatedByMeLeaveTabBarWidget(
                                                                leaveTypes:
                                                                    leaveTypeNames,
                                                                absenceData:
                                                                    deniedAbsences ??
                                                                    [],
                                                                status: 'rejected',
                                                                iconClicked:
                                                                    'Apply Leave',
                                                                selectedTabIndex:
                                                                    _selectedTabIndex,
                                                              )
                                                              : (noMatchDenied
                          ? noSearchResultWidget(context)
                          : noDataFoundWidget(context));
                                                        },
                                                        loading:
                                                            () => Container(),
                                                        error:
                                                            (err, stack) =>
                                                                noMatchFound ? noSearchResultWidget(context) : noDataFoundWidget(
                                                                  context,
                                                                ),
                                                      ),
                                                  ref
                                                      .watch(
                                                        withdrawnListNotifierProvider,
                                                      )
                                                      .when(
                                                        data: (withdrawnAbsences) {
                                                          if (!mounted)
                                                            return const SizedBox.shrink();
                                                          return withdrawnAbsences
                                                                  .isNotEmpty
                                                              ? CreatedByMeLeaveTabBarWidget(
                                                                leaveTypes:
                                                                    leaveTypeNames,
                                                                absenceData:
                                                                    withdrawnAbsences ??
                                                                    [],
                                                                status: 'withdrawn',
                                                                iconClicked:
                                                                    'Apply Leave',
                                                                selectedTabIndex:
                                                                    _selectedTabIndex,
                                                              )
                                                              :(noMatchWithdrawn
                          ? noSearchResultWidget(context)
                          : noDataFoundWidget(context));
                                                        },
                                                        loading:
                                                            () => Container(),
                                                        error:
                                                            (err, stack) =>
                                                               noMatchFound ? noSearchResultWidget(context) : noDataFoundWidget(
                                                                  context,
                                                                ),
                                                      ),
                                                  ref
                                                      .watch(draftListNotifierProvider)
                                                      .when(
                                                        data: (draftAbsences) {
                                                          if (!mounted)
                                                            return const SizedBox.shrink();
                                                          return draftAbsences
                                                                  .isNotEmpty
                                                              ? CreatedByMeLeaveTabBarWidget(
                                                                leaveTypes:
                                                                    leaveTypeNames,
                                                                absenceData:
                                                                    draftAbsences ?? [],
                                                                status: 'draft',
                                                                iconClicked:
                                                                    'Apply Leave',
                                                                selectedTabIndex:
                                                                    _selectedTabIndex,
                                                              )
                                                              : (noMatchDraft
                          ? noSearchResultWidget(context)
                          : noDataFoundWidget(context));
                                                        },
                                                        loading:
                                                            () => Container(),
                                                        error:
                                                            (err, stack) =>
                                                                noDataFoundWidget(
                                                                  context,
                                                                ),
                                                      ),
                                                ],
                                              ),
                                            ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            if (_selectedTabIndex == 1)
                              DefaultTabController(
                                length: 1,
                                child: Builder(
                                  builder: (context) {
                                    TabController tabController =
                                        DefaultTabController.of(context);
                                    return Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(left: 6.0),
                                          child: CustomStatusTabBar(
                                            controller: tabController,
                                            tabs: AssignToMetabs,
                                          ),
                                        ),
                                         SizedBox(height: 12.h),
                                        LeaveTypeSearchRow(
                                          tabIndex: _selectedTabIndex,
                                          showSearchField: showSearchField,
                                          searchController: searchController,
                                          leaveTypes: leaveTypes,
                                          onSearchNoMatchFound: onSearchNoMatchFound,
                                          searchForLeaveType: searchForTask,
                                          fetchAllAbsences: fetchAllTask,
                                          toggleSearch: () {
                                            setState(() {
                                              searchController.clear();
                                              showSearchField = !showSearchField;
                                              if (showSearchField) {
                                                Future.delayed(
                                                  Duration(milliseconds: 100),
                                                  () {
                                                    searchFocusNode.requestFocus();
                                                  },
                                                );
                                              }
                                            });
                                          },
                                          filterWidget: FilterBasedOnDates(
                                            isAbsence: true,
                                            selectedLeaveType: _selectedLeaveType,
                                            dateRangeProvider:
                                                applyLeaveDateRangeProvider,
                                          ),
                                          searchFocusNode: searchFocusNode,
                                        ),
                                        Consumer(
                                          builder: (context, ref, child) {
                                            // final dateRange = ref.watch(
                                            //   dateRangeProvider,
                                            // );
                                            final dateRange = ref.watch(
                                              applyLeaveDateRangeProvider,
                                            );
                    
                                            if (dateRange == null ||
                                                dateRange['startDate'] == null ||
                                                dateRange['endDate'] == null) {
                                              return SizedBox(height: 6.h,);
                                            }
                    
                                            final startDate = DateFormat(
                                              'dd MMM yyyy',
                                            ).format(dateRange['startDate']!);
                                            final endDate = DateFormat(
                                              'dd MMM yyyy',
                                            ).format(dateRange['endDate']!);
                    
                                            return Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 10),
                                                  child: OpenSansText(
                                                    '$startDate - $endDate',
                                                    fontSize: 14,
                                                    color: AppColors.viewColor,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    // ref
                                                    //     .read(
                                                    //       dateRangeProvider
                                                    //           .notifier,
                                                    //     )
                                                    //     .state = null;
                                                    ref
                                                        .read(
                                                          applyLeaveDateRangeProvider
                                                              .notifier,
                                                        )
                                                        .state = null;
                                                         ref.read(pendingAbsenceTasksNotifierProvider.notifier).clearDateFilters();
                                                  },
                                                  child: OpenSansText(
                                                    'clear filters',
                                                    fontSize: 12,
                                                    color: AppColors.viewColor,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        noMatchForTasks
                                            ? noSearchResultWidget(context)
                                            : Container(
                                              height: 400.h, // Adjust height as needed
                                              child: TabBarView(
                                                children: [
                                                  ref
                                                      .watch(
                                                        pendingTasksProvider(
                                                          searchQuery,
                                                        ),
                                                      )
                                                      .when(
                                                        loading:
                                                            () => const Center(
                                                              child:
                                                                  CustomLoadingWidget(),
                                                            ),
                                                        error:
                                                            (error, stack) => Center(
                                                              child: Text(
                                                                'Error: $error',
                                                              ),
                                                            ),
                                                        data: (taskResponse) {
                                                          final tasks =
                                                              taskResponse.items;
                                                                if (tasks.isEmpty && !noMatchForTasks) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (mounted) {
                                  setState(() {
                                    noMatchForTasks = true;
                                  });
                                }
                              });
                            }
                                                          return tasks.isNotEmpty
                                                              ? ReusableCard(
                                                                
onApprovalSuccess: () {
                                                              ref
                                                                  .read(
                                                                    taskRefreshTriggerProvider
                                                                        .notifier,
                                                                  )
                                                                  .state++;
                                                            },

                                                                forAbsence: true,
                                                                status: 'pending',
                                                                showDownloadButton:
                                                                    false,
                                                                tasks: taskResponse,
                                                                selectedTabIndex: 0,
                                                                iconClicked: 'Approval',
                                                                selectedStatus:
                                                                    'ASSIGNED',
                                                                isVisible: true,
                                                                hideContainerForScreens:
                                                                    false,
                                                              )
                                                              : noDataFoundWidget(
                                                                context,
                                                                message:
                                                                    messageAssignedToMe,
                                                              );
                                                        },
                                                      ),
                    
                                                  // Approved tasks (COMPLETED)
                                                  // ref
                                                  //     .watch(
                                                  //       approvedTasksProvider(
                                                  //         searchQuery,
                                                  //       ),
                                                  //     )
                                                  //     .when(
                                                  //       loading:
                                                  //           () => const Center(
                                                  //             child:
                                                  //                 CustomLoadingWidget(),
                                                  //           ),
                                                  //       error:
                                                  //           (error, stack) => Center(
                                                  //             child: Text(
                                                  //               'Error: $error',
                                                  //             ),
                                                  //           ),
                                                  //       data: (taskResponse) {
                                                  //         final tasks =
                                                  //             taskResponse.items;
                                                  //         return tasks.isNotEmpty
                                                  //             ? ReusableCard(
                                                  //               forAbsence: true,
                    
                                                  //               status: 'approved',
                                                  //               showDownloadButton:
                                                  //                   false,
                                                  //               tasks: taskResponse,
                                                  //               selectedTabIndex: 1,
                                                  //               iconClicked: 'Approval',
                                                  //               selectedStatus:
                                                  //                   'COMPLETED',
                                                  //               isVisible: true,
                                                  //               hideContainerForScreens:
                                                  //                   false,
                                                  //             )
                                                  //             : noDataFoundWidget(
                                                  //               context,
                                                  //               message:
                                                  //                   messageAssignedToMe,
                                                  //             );
                                                  //       },
                                                  //     ),
                    
                                                  // // Rejected tasks (SUSPENDED)
                                                  // ref
                                                  //     .watch(
                                                  //       rejectedTasksProvider(
                                                  //         searchQuery,
                                                  //       ),
                                                  //     )
                                                  //     .when(
                                                  //       loading:
                                                  //           () => const Center(
                                                  //             child:
                                                  //                 CustomLoadingWidget(),
                                                  //           ),
                                                  //       error:
                                                  //           (error, stack) => Center(
                                                  //             child: Text(
                                                  //               'Error: $error',
                                                  //             ),
                                                  //           ),
                                                  //       data: (taskResponse) {
                                                  //         final tasks =
                                                  //             taskResponse.items;
                                                  //         return tasks.isNotEmpty
                                                  //             ? ReusableCard(
                                                  //               forAbsence: true,
                    
                                                  //               status: 'rejected',
                                                  //               showDownloadButton:
                                                  //                   false,
                                                  //               tasks: taskResponse,
                                                  //               selectedTabIndex: 2,
                                                  //               iconClicked: 'Approval',
                                                  //               selectedStatus:
                                                  //                   'SUSPENDED',
                                                  //               isVisible: true,
                                                  //               hideContainerForScreens:
                                                  //                   false,
                                                  //             )
                                                  //             : noDataFoundWidget(
                                                  //               context,
                                                  //               message:
                                                  //                   messageAssignedToMe,
                                                  //             );
                                                  //       },
                                                  //     ),
                    
                                                  // // Withdrawn tasks (WITHDRAWN)
                                                  // ref
                                                  //     .watch(
                                                  //       withdrawnTasksProvider(
                                                  //         searchQuery,
                                                  //       ),
                                                  //     )
                                                  //     .when(
                                                  //       loading:
                                                  //           () => const Center(
                                                  //             child:
                                                  //                 CustomLoadingWidget(),
                                                  //           ),
                                                  //       error:
                                                  //           (error, stack) => Center(
                                                  //             child: Text(
                                                  //               'Error: $error',
                                                  //             ),
                                                  //           ),
                                                  //       data: (taskResponse) {
                                                  //         final tasks =
                                                  //             taskResponse.items;
                                                  //         return tasks.isNotEmpty
                                                  //             ? ReusableCard(
                                                  //               forAbsence: true,
                    
                                                  //               status: 'withdrawn',
                                                  //               showDownloadButton:
                                                  //                   false,
                                                  //               tasks: taskResponse,
                                                  //               selectedTabIndex: 3,
                                                  //               iconClicked: 'Approval',
                                                  //               selectedStatus:
                                                  //                   'WITHDRAWN',
                                                  //               isVisible: true,
                                                  //               hideContainerForScreens:
                                                  //                   false,
                                                  //             )
                                                  //             : noDataFoundWidget(
                                                  //               context,
                                                  //               message:
                                                  //                   messageAssignedToMe,
                                                  //             );
                                                  //       },
                                                  //     ),
                                                ],
                                              ),
                                            ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            floatingActionButton: Padding(
              padding: const EdgeInsets.only(bottom: 52.0),
              child: FloatingActionButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    isDismissible: true,
                    enableDrag: true,
                    builder: (context) {
                      return DraggableScrollableSheet(
                        initialChildSize: 0.6,
                        minChildSize: 0.3,
                        maxChildSize: 0.85,
                        expand: false,
                        builder: (context, scrollController) {
                          return ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20.0),
                              topRight: Radius.circular(20.0),
                            ),
                            child: LeaveTypesForNavigation(
                              leaveTypes: leaveTypeNames,
                              scrollController: scrollController,
                              isFromDashboard: true,
                            ),
                          );
                        },
                      );
                    },
                  );
                },
                shape: const CircleBorder(),
                child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
              ),
            ),
            bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
          ),
      )),
         if (_initialLoadingComplete && isLoadingTabsData) fullPageLoader(),
      
      ],
    );
  }
}

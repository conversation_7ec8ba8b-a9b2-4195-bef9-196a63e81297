import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/utils/style/colors.dart';
import '../provider/storiesProvider.dart';

class ChildrenBottomSheet extends StatefulWidget {
  const ChildrenBottomSheet({Key? key}) : super(key: key);

  @override
  _ChildrenBottomSheetState createState() => _ChildrenBottomSheetState();
}

class _ChildrenBottomSheetState extends State<ChildrenBottomSheet> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final provider = Provider.of<StoriesProvider>(context, listen: false);
        provider.loadStudentsForParent();
      } catch (e) {
        debugPrint('Error accessing StoriesProvider in ChildrenBottomSheet: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StoriesProvider>(
      builder: (context, provider, child) {
        final students = provider.studentsList;
        
        if (provider.loadingStudents) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              children: [
                Center(
                  child: CustomSvgImage(
                    imageName: "Rectangle12231",
                    height: 5,
                    width: 30,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    SvgImage24x24('assets/images/children.svg'),
                    SizedBox(width: 8),
                    OpenSans600Large(16, 'Children', AppColors.blackColor),
                  ],
                ),
                SizedBox(height: 8),
                const Divider(color: AppColors.dividerColor1),
                Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ],
            ),
          );
        }
        
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            children: [
              Center(
                child: CustomSvgImage(
                  imageName: "Rectangle12231",
                  height: 5,
                  width: 30,
                ),
              ),
              SizedBox(height: 12),
              Row(
                children: [
                  SvgImage24x24('assets/images/children.svg'),
                  SizedBox(width: 8),
                  OpenSans600Large(16, 'Children', AppColors.blackColor),
                ],
              ),
              SizedBox(height: 8),
              const Divider(color: AppColors.dividerColor1),
              Expanded(
                child: students.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.child_care, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            OpenSans400Large(14, 'No children found', Colors.grey),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: students.length,
                        itemBuilder: (context, index) {
                          final student = students[index];
                          final isSelected = provider.selectedStudentIds.contains(student.id);

                          return CheckboxListTile(
                            value: isSelected,
                            onChanged: (bool? value) {
                              provider.toggleStudent(student.id);
                            },
                            activeColor: AppColors.viewColor,
                            checkColor: AppColors.whiteColor,
                            side: BorderSide(color: AppColors.viewColor),
                            controlAffinity: ListTileControlAffinity.leading,
                            title: Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: Colors.transparent,
                                  radius: 20,
                                  child: student.profileKey?.isNotEmpty == true
                                      ? FutureBuilder<Uint8List?>(
                                          future: provider.getImageBytes(student.profileKey!),
                                          builder: (context, snapshot) {
                                            if (snapshot.hasData && snapshot.data != null) {
                                              return ClipOval(
                                                child: Image.memory(
                                                  snapshot.data!,
                                                  fit: BoxFit.cover,
                                                  width: 40,
                                                  height: 40,
                                                  errorBuilder: (context, error, stackTrace) {
                                                    return Image.asset('assets/images/maleChildAvatar.png');
                                                  },
                                                ),
                                              );
                                            }
                                            return Image.asset('assets/images/maleChildAvatar.png');
                                          },
                                        )
                                      : Image.asset('assets/images/maleChildAvatar.png'),
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      OpenSans600Large(12, student.fullNameEn, AppColors.blackColor),
                                      SizedBox(height: 2),
                                      OpenSans400Large(12, '${student.grade} - ${student.section}', AppColors.lightBlack),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
              SizedBox(height: 10),
              SizedBox(
                width: double.infinity,
                child: SubmitRequestButton(
                  text: "Done",
                  onPressed: () {
       final provider = Provider.of<StoriesProvider>(context, listen: false);
       final selectedStudents = provider.studentsList
          .where((student) => provider.selectedStudentIds.contains(student.id))
          .map((student) => {
            'id': student.id,
            'name': student.fullNameEn,
            'profileKey': student.profileKey,
          })
          .toList();
                    Navigator.of(context).pop(selectedStudents);
                  },
                ),
              ),
            ],
          ),
        );
      }
    );
  }
}
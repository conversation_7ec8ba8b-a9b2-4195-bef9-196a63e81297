import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/claim/components/loanadvanceform.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/claim/components/claimtype.dart';
import 'package:seawork/components/widget/customSubmitConfirmDialog.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';

class LoanAdvanceForm extends StatefulWidget {
  final String loanType;
  final bool showClaimTypeDropdown;
  final bool hideAppBar;
  final bool showLoanTypeField;
  final bool hideLoanTypeField;
  final String? initialClaimType; // Added this parameter

  const LoanAdvanceForm({
    Key? key,
    this.showClaimTypeDropdown = false,
    required this.loanType,
    this.hideAppBar = false,
    this.showLoanTypeField = true,
    this.hideLoanTypeField = false,
    this.initialClaimType, // Added this parameter
  }) : super(key: key);

  @override
  State<LoanAdvanceForm> createState() => _LoanAdvanceFormState();
}

class _LoanAdvanceFormState extends State<LoanAdvanceForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<LoanFormWidgetState> _loanFormKey =
      GlobalKey<LoanFormWidgetState>();

  // Track if form is valid
  bool _isFormValid = false;
  String? currentClaimType; // Added claim type tracking

  @override
  void initState() {
    super.initState();
    // Initialize claim type with either initialClaimType or loanType
    currentClaimType = widget.initialClaimType ?? widget.loanType;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title:
            widget.showClaimTypeDropdown
                ? 'Add claims'
                : 'Loan/advance request',
        showActionIcon: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 32),
        child: SingleChildScrollView(
          child: Column(
            children: [
              if (widget.showClaimTypeDropdown) ...[
                HeadingText(text: "Claim type", hasAsterisk: true),
                SizedBox(height: 8),
                ClaimTypeSelector(
                  initialClaimType: currentClaimType,
                  onClaimTypeChanged: (newClaimType) {
                    setState(() {
                      currentClaimType = newClaimType;
                    });
                  },
                ),
                SizedBox(height: 24),
              ],
              LoanFormWidget(
                showLoanTypeField:
                    widget.showLoanTypeField && !widget.hideLoanTypeField,
                key: _loanFormKey,
                formKey: _formKey,
                loanType: widget.loanType,
                onChanged: () {
                  setState(() {
                    _isFormValid =
                        _loanFormKey.currentState?.isFormValid ?? false;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 32.0),
        child: ElevatedButton(
          onPressed:
              _isFormValid
                  ? () {
                    showDialog(
                      context: context,
                      builder:
                          (context) => CustomSubmitConfirmDialog(
                            content1: 'Claim',
                            type: 'Claim',
                            content3: "Loan/advance request",
                            message:
                                'Your claim has been submitted and ready to be reviewed',
                            onClose: () => context.go('/claim'),
                            documentType: 'Loan/advance request',
                            document: CustomSubmitConfirmDialog,
                          ),
                    );
                  }
                  : null,
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 56),
            backgroundColor:
                _isFormValid
                    ? const Color(0xFF395062)
                    : const Color(0xFFCDD6DC),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: DmSansText(
            'Submit request',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.whiteColor,
          ),
        ),
      ),
     ) );
  }
}

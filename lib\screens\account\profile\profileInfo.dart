import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/screens/account/profile/components/communicationSection.dart';
import 'package:seawork/screens/account/profile/components/customProfilePictureWidget.dart';
import 'package:seawork/screens/account/profile/provider/profileProvider.dart';

import 'package:seawork/utils/style/colors.dart';

class ProfileInfo extends ConsumerStatefulWidget {
  final String personId;

  const ProfileInfo({super.key, required this.personId});

  @override
  ConsumerState<ProfileInfo> createState() => _ProfileInfoState();
}

class _ProfileInfoState extends ConsumerState<ProfileInfo> {
  @override
  Widget build(BuildContext context) {
    final employeeAsync = ref.watch(employeeByIdProvider(widget.personId));

   return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:SecondaryScaffoldWithAppBar(
      context,
      'Profile info',
      SvgImage24x24('assets/images/appbackbutton.svg'),
      () => Navigator.pop(context),
      bodyItem: employeeAsync.when(
        data: (employee) {
          if (employee == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.person_off,
                    size: 64,
                    color: AppColors.blackColor.withOpacity(0.6),
                  ),
                  SizedBox(height: 16),
                  DMSans700Large(
                    16,
                    'Employee Not Found',
                    AppColors.blackColor,
                  ),
                  SizedBox(height: 8),
                  OpenSans400Large(
                    12,
                    'The selected employee could not be found.',
                    AppColors.blackColor.withOpacity(0.6),
                  ),
                ],
              ),
            );
          }

          return _buildProfileContent(employee);
        },
        loading:
            () => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColors.blackColor),
                  SizedBox(height: 16),
                  OpenSans400Large(
                    12,
                    'Loading profile...',
                    AppColors.blackColor.withOpacity(0.6),
                  ),
                ],
              ),
            ),
        error:
            (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.blackColor.withOpacity(0.6),
                  ),
                  SizedBox(height: 16),
                  DMSans700Large(
                    16,
                    'Error Loading Profile',
                    AppColors.blackColor,
                  ),
                  SizedBox(height: 8),
                  OpenSans400Large(
                    12,
                    'Please try again later.',
                    AppColors.blackColor.withOpacity(0.6),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () =>
                            ref.refresh(employeeByIdProvider(widget.personId)),
                    child: Text('Retry'),
                  ),
                ],
              ),
            ),
  )),
    );
  }

  Widget _buildProfileContent(Map<String, dynamic> employee) {
    // Extract data from API response
    final displayName = employee['DisplayName']?.toString() ?? 'Unknown';
    final workEmail = employee['WorkEmail']?.toString() ?? 'No email';

    final personNumber = employee['PersonNumber']?.toString() ?? '';

    String fullName = displayName;

    // Combine address parts (3,2,1) with commas
    final addressLine1 = employee['AddressLine1']?.toString() ?? '';
    final addressLine2 = employee['AddressLine2']?.toString() ?? '';
    final addressLine3 = employee['AddressLine3']?.toString() ?? '';
    final city = employee['City']?.toString() ?? '';

    List<String> addressParts = [];
    if (addressLine3.isNotEmpty) addressParts.add(addressLine3);
    if (addressLine2.isNotEmpty) addressParts.add(addressLine2);
    if (addressLine1.isNotEmpty) addressParts.add(addressLine1);
    if (city.isNotEmpty) addressParts.add(city);

    String fullAddress =
        addressParts.isNotEmpty
            ? addressParts.join(', ')
            : 'No address available';

    return Column(
      children: [
        const SizedBox(height: 20),
        ProfileStackWidget(
          image: AssetImage('assets/images/placeholder_profile.png'),
        ), // Placeholder image
        const SizedBox(height: 8),
        DMSans700Large(16, fullName, AppColors.blackColor),
        const SizedBox(height: 8),
        OpenSans400Large(12, workEmail, AppColors.blackColor),
        const SizedBox(height: 4),
        OpenSans400Large(
          12,
          fullAddress,
          AppColors.blackColor.withOpacity(0.45),
        ),
        const SizedBox(height: 32),
        CommunicationSection(employee: employee),
        // Manager section would need additional API call to get manager data
        // if (hasManager) ManagerSection(manager: managerData),
      ],
    );
  }

  String _buildPhoneNumber(
    String? countryCode,
    String? areaCode,
    String? number,
  ) {
    if (number == null || number.isEmpty) return 'Not available';

    String phone = number;
    if (areaCode != null && areaCode.isNotEmpty) {
      phone = '$areaCode$number';
    }
    if (countryCode != null && countryCode.isNotEmpty) {
      phone = '+$countryCode $phone';
    }

    return phone;
  }
}

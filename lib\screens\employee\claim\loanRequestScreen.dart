import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:go_router/go_router.dart';

class ScreenLoan extends StatefulWidget {
  const ScreenLoan({super.key, this.hideAppBar = false});
  final bool hideAppBar;

  @override
  State<ScreenLoan> createState() => _ScreenLoanState();
}

class _ScreenLoanState extends State<ScreenLoan> {
  final _formKey = GlobalKey<FormState>();
  String? selectedType;
  bool isBottomSheetOpen = false;

  final List<String> loanTypes = ["Salary advance", "Loan against EOS request"];

  final InputDecoration textFieldDecoration = InputDecoration(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Color(0xFFCDD6DC), width: 1),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Color(0xFFCDD6DC), width: 1),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Color(0xFFCDD6DC), width: 1),
    ),
    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
  );

  void _showLoanTypeBottomSheet() async {
    setState(() {
      isBottomSheetOpen = true;
    });

    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          height: 161,
          width: double.infinity,
          child: ListView(
            shrinkWrap: true,
            children: [
              SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [CustomSvgImage(imageName: "Rectangle_icon")],
              ),
              // Salary Advance
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                child: Material(
                  color:
                      selectedType == "Salary advance"
                          ? const Color(0xFFCDD6DC)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    highlightColor: AppColors.microinteraction,
                    splashColor: AppColors.microinteraction,
                    onTap: () async {
                      // Wait so the ripple effect shows BEFORE closing
                      await Future.delayed(const Duration(milliseconds: 100));
                      setState(() {
                        selectedType = "Salary advance";
                      });
                      Navigator.pop(context);
                      _navigateToLoanDetails();
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: OpenSansText(
                          "Salary advance",
                          fontSize: 16,
                          fontWeight:
                              selectedType == "Salary advance"
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // Loan against EOS request
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                child: Material(
                  color:
                      selectedType == "Loan against EOS request"
                          ? const Color(0xFFCDD6DC)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    highlightColor: AppColors.microinteraction,
                    splashColor: AppColors.microinteraction,
                    onTap: () async {
                      await Future.delayed(const Duration(milliseconds: 120));

                      // Now safely trigger setState AFTER splash is shown
                      if (mounted) {
                        setState(() {
                          selectedType = "Loan against EOS request";
                        });
                        Navigator.pop(context);
                        _navigateToLoanDetails();
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: OpenSansText(
                          "Loan against EOS request",
                          fontSize: 16,
                          fontWeight:
                              selectedType == "Loan against EOS request"
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
    setState(() {
      isBottomSheetOpen = false;
    });
  }

  void _navigateToLoanDetails() {
    if (selectedType != null) {
      context.push('/loan-advance-form', extra: {'loanType': selectedType!});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true, // Allows the back button/swipe gesture to work
      onPopInvoked: (didPop) {
        if (!didPop) {
          context.pop(); // Handle the back navigation
        }
      },
      child:Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar:
          widget.hideAppBar
              ? null
              : CustomAppBar(
                title: 'Loan/advance request ',
                showActionIcon: true,
              ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),
                HeadingText(
                  text: 'Loan/advance request type',
                  hasAsterisk: true,
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 43,
                  width: double.infinity,
                  child: TextFormField(
                    readOnly: true,
                    onTap: _showLoanTypeBottomSheet,
                    decoration: textFieldDecoration.copyWith(
                      filled: true,
                      fillColor: AppColors.whiteColor,
                      hintText: 'Select type',
                      hintStyle: GoogleFonts.openSans(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: 1.35,
                        color: AppColors.lightGreyshade,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: CustomSvgImage(
                          imageName:
                              isBottomSheetOpen ? "Arrowup" : "ArrowLeft",
                          color: AppColors.viewColor,
                        ),
                      ),
                      suffixIconConstraints: const BoxConstraints(
                        minHeight: 24,
                        minWidth: 24,
                      ),

                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          color: Color(0xFFCDD6DC),
                          width: 1,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
     ) );
  }
}

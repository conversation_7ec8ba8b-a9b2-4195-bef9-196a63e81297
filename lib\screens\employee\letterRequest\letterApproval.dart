import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/scroll/scrollPaginationContainer.dart';
import 'package:seawork/components/widget/customLoaderWithMessages.dart';
import 'package:seawork/components/widget/customMainTab.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';
import 'package:seawork/screens/employee/letterRequest/components/assignedToMeListItem.dart';
import 'package:seawork/screens/employee/letterRequest/components/documentListItem.dart';
import 'package:seawork/screens/employee/letterRequest/components/request_letter_bottom_sheet.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_statusCount_model.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customTabbar.dart';

// Add this provider if not already defined
final dateRangeProvider = StateProvider<Map<String, DateTime>?>((ref) => null);

class LetterApprovals extends ConsumerStatefulWidget {
  final String iconClicked;
  static String tag = 'letterRequestTest';
  const LetterApprovals({Key? key, required this.iconClicked})
    : super(key: key);

  @override
  ConsumerState<LetterApprovals> createState() => _LetterApprovalsState();
}

class _LetterApprovalsState extends ConsumerState<LetterApprovals> {
  int _selectedTabIndex = 0;
  String _selectedStatus = "pending";
  String _renderingStatus = '';
  String _searchQuery = "";
  bool isLoading = false;
  bool isCreatedByMeOnly = false;
  bool _initialLoadingComplete = false;

  void _handleSearchQueryChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    final notifier =
        _selectedStatus == "pending"
            ? ref.read(letterRequestPendingProvider.notifier)
            : ref.read(letterRequestApprovedProvider.notifier);
    notifier.search(query);
  }

  void _clearFilters() {
    ref.read(dateRangeProvider.notifier).state = null;
    ref.refresh(letterRequestPendingProvider);
    ref.refresh(letterRequestApprovedProvider);
    _handleSearchQueryChanged("");
  }

  @override
  Widget build(BuildContext context) {
    final documentsAsync = ref.watch(GetDocumentRecordStatusProvider);
    final pendingDocumentsAssignedToMeAsync = ref.watch(
      pendingDocumentTasksProvider,
    );
    final approvedDocumentsAssignedToMeAsync = ref.watch(
      approvedDocumentTasksProvider,
    );

    // Track loading states for all tabs
    final isPendingDocumentsLoading =
        ref.watch(letterRequestPendingProvider).isLoading;
    final isApprovedDocumentsLoading =
        ref.watch(letterRequestApprovedProvider).isLoading;
    final isPendingTasksLoading = pendingDocumentsAssignedToMeAsync.isLoading;
    final isApprovedTasksLoading = approvedDocumentsAssignedToMeAsync.isLoading;

    // Combined loading state for tabs
    final isLoadingTabsData =
        isPendingDocumentsLoading ||
        isApprovedDocumentsLoading ||
        isPendingTasksLoading ||
        isApprovedTasksLoading;

    return Stack(
      children: [
        PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child:SecondaryScaffoldWithAppBar(
          context,
          widget.iconClicked,
          SvgImage24x24('assets/images/appbackbutton.svg'),
          () {
            Navigator.pop(context);
          },
          showHelpIcon: true,
          bodyItem: SafeArea(
            child: documentsAsync.when(
              data: (docStatus) {
                if (!_initialLoadingComplete) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) setState(() => _initialLoadingComplete = true);
                  });
                }
                return _buildLoadedBody(
                  context,
                  docStatus,
                  pendingDocumentsAssignedToMeAsync.records,
                );
              },
              loading: () => const Center(child: RotatingLoaderWithMessages()),
              error:
                  (err, stack) =>
                      Center(child: Text("Error loading document status")),
            ),
          ),
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(bottom: 52.0),
            child: FloatingActionButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  isDismissible: true,
                  enableDrag: true,
                  builder: (context) {
                    return DraggableScrollableSheet(
                      initialChildSize: 0.6,
                      minChildSize: 0.3,
                      maxChildSize: 0.85,
                      expand: false,
                      builder: (context, scrollController) {
                        return ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20.0),
                            topRight: Radius.circular(20.0),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(20.0),
                                topRight: Radius.circular(20.0),
                              ),
                            ),
                            child: RequestLetterBottomSheet(
                              'all',
                              scrollController: scrollController,
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
              shape: const CircleBorder(),
              child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
            ),
          ),
          bottomNavigationBar: CustomBottomNavigationBar(
            onTap: (index) {
              print('Bottom navigation item tapped: $index');
            },
   ) ),
        ),
        if (_initialLoadingComplete && isLoadingTabsData)
          Positioned.fill(child: fullPageLoader()),
      ],
    );
  }

  Widget _buildTabItem(String title, int index, int count) {
    final String countText =
        count > 999 ? '999+' : (count > 99 ? '99+' : '$count');
    final int digitLength = countText.length;

    double circleSize = 16;
    double fontSize = 10;

    if (digitLength >= 3) {
      circleSize = 20;
      fontSize = 11;
    }
    if (digitLength >= 4) {
      circleSize = 24;
      fontSize = 12;
    }
    return Container(
      height: 30,
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color:
            _selectedTabIndex == index
                ? AppColors.viewColor
                : AppColors.whiteColor,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: AppColors.boxshadowcolor.withOpacity(0.25),
            offset: const Offset(0, 0),
            blurRadius: 9.6,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            OpenSansText(
              title,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color:
                  _selectedTabIndex == index
                      ? AppColors.whiteColor
                      : AppColors.viewColor,
            ),
            const SizedBox(width: 8),
            Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: circleSize,
                  height: circleSize,
                  decoration: const BoxDecoration(
                    color: AppColors.Orange,
                    shape: BoxShape.circle,
                  ),
                ),
                RobotoText(
                  count > 99 ? '99+' : '$count',
                  fontSize: fontSize,
                  fontWeight: FontWeight.w400,
                  color: AppColors.whiteColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedBody(
    BuildContext context,
    DocumentStatusCount docStatus,
    List<TaskItem> taskItems,
  ) {
    final pendingState = ref.watch(letterRequestPendingProvider);
    final approvedState = ref.watch(letterRequestApprovedProvider);
    final statusProvider = ref.watch(documentStatusProvider);
    final paginationState =
        statusProvider == "pending" ? pendingState : approvedState;
    final notifier =
        statusProvider == "pending"
            ? ref.read(letterRequestPendingProvider.notifier)
            : ref.read(letterRequestApprovedProvider.notifier);
    final assignedToMenotifier = ref.read(documentTaskCountsProvider.notifier);
    final pendingCount = assignedToMenotifier.getCount('ASSIGNED');
    final approvedCount = assignedToMenotifier.getCount('COMPLETED');
    final totalCountAssignedtoMe = pendingCount + approvedCount;
    final totalCountCreatedByMe =
        docStatus.pendingItems + docStatus.approvedItems;

    return Padding(
      padding: EdgeInsets.only(
        top: isCreatedByMeOnly ? MediaQuery.of(context).padding.top : 0,
        bottom: isCreatedByMeOnly ? MediaQuery.of(context).padding.bottom : 0,
      ),
      child: Column(
        children: [
          SizedBox(height: isCreatedByMeOnly ? 8 : 0),

          // Date range filter display
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Consumer(
              builder: (context, ref, child) {
                final dateRange = ref.watch(dateRangeProvider);
                if (dateRange == null ||
                    dateRange['startDate'] == null ||
                    dateRange['endDate'] == null) {
                  return const SizedBox.shrink();
                }

                final startDate = DateFormat(
                  'dd MMM yyyy',
                ).format(dateRange['startDate']!);
                final endDate = DateFormat(
                  'dd MMM yyyy',
                ).format(dateRange['endDate']!);

                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OpenSansText(
                        '$startDate - $endDate',
                        fontSize: 14,
                        color: AppColors.viewColor,
                        fontWeight: FontWeight.w400,
                      ),
                      TextButton(
                        onPressed: _clearFilters,
                        child: OpenSansText(
                          'clear filters',
                          fontSize: 12,
                          color: AppColors.viewColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          if (!isCreatedByMeOnly && totalCountAssignedtoMe > 0)
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Container(
                width: double.infinity,
                height: 38,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomMainTabBar(
                  selectedIndex: _selectedTabIndex,
                  onTabSelected: (index) {
                    setState(() {
                      _selectedTabIndex = index;
                    });
                  },
                  tabs: [
                    TabItem(
                      label: 'Created by me',
                      badgeCount: totalCountCreatedByMe,
                    ),
                    TabItem(
                      label: 'Assigned to me',
                      badgeCount: totalCountAssignedtoMe,
                    ),
                  ],
                ),
              ),
            ),
          SizedBox(height: isCreatedByMeOnly ? 8 : 0),

          if (_selectedTabIndex == 0)
            DefaultTabController(
              length: 3,
              child: Expanded(
                child: CustomTabBar(
                  counts: [docStatus.pendingItems, docStatus.approvedItems],
                  onTabChanged: (status) {
                    ref.read(documentStatusProvider.notifier).state = status;
                  },
                  length: 2,
                  iconClicked: 'Letter request',
                  onSearchQueryChanged: notifier.search,
                  isCreatedByMe: true,
                  buildTabContent: (
                    String status, {
                    int? selectedTabIndex,
                    String? iconClicked,
                  }) {
                    return Column(
                      children: [
                        const SizedBox(height: 10),
                        Expanded(
                          child:
                              paginationState.isLoading
                                  ? Center(child: Container())
                                  : (!paginationState.isLoading &&
                                      !paginationState.hasMore &&
                                      paginationState.records.isEmpty)
                                  ? (paginationState.documentType!.isNotEmpty
                                      ? noSearchResultWidget(context)
                                      : noDataFoundWidget(context))
                                  : ScrollPaginationContainer(
                                    onRefresh: () async {
                                      await notifier.refresh();
                                    },
                                    onLoadMore: () async {
                                      await notifier.loadMoreRecords();
                                    },
                                    isLoading: paginationState.isLoading,
                                    hasMore: paginationState.hasMore,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                    ),
                                    children: [
                                      DocumentListItem(
                                        context,
                                        documentList: paginationState.records,
                                        status: status,
                                        showDownloadButton: true,
                                        selectedStatus: _selectedStatus,
                                        selectedTabIndex: 0,
                                        iconClicked: 'Letter request',
                                        isVisible: false,
                                        hideContainerForScreens: true,
                                        removeTopBottomPadding: true,
                                      ),
                                    ],
                                  ),
                        ),
                      ],
                    );
                  },
                  isLoading: paginationState.isLoading,
                ),
              ),
            ),
          if (_selectedTabIndex == 1)
            Consumer(
              builder: (context, ref, _) {
                final pendingAsync = ref.watch(pendingDocumentTasksProvider);
                final approvedAsync = ref.watch(approvedDocumentTasksProvider);
                final statusProvider = ref.watch(documentStatusProvider);
                final paginationStateTask =
                    statusProvider == "pending" ? pendingAsync : approvedAsync;
                final countNotifier = ref.read(
                  documentTaskCountsProvider.notifier,
                );
                final pendingCount = countNotifier.getCount('COMPLETED');
                final assignedToMeNotifier =
                    statusProvider == "pending"
                        ? ref.read(pendingDocumentTasksProvider.notifier)
                        : ref.read(approvedDocumentTasksProvider.notifier);
                return DefaultTabController(
                  length: 3,
                  child: Expanded(
                    child: CustomTabBar(
                      counts: [docStatus.pendingItems, pendingCount],
                      onTabChanged: (status) {
                        ref.read(documentStatusProvider.notifier).state =
                            status;
                      },
                      length: 2,
                      iconClicked: 'Letter request',
                      onSearchQueryChanged: assignedToMeNotifier.search,
                      isAssignedToMe: true,
                      buildTabContent: (
                        String status, {
                        int? selectedTabIndex,
                        String? iconClicked,
                      }) {
                        return Column(
                          children: [
                            const SizedBox(height: 10),
                            Expanded(
                              child:
                                  paginationStateTask.isLoading
                                      ? Center(child: Container())
                                      : (!paginationState.isLoading &&
                                          !paginationState.hasMore &&
                                          paginationState.records.isEmpty)
                                      ? (paginationState
                                              .documentType!
                                              .isNotEmpty
                                          ? noSearchResultWidget(context)
                                          : noDataFoundWidget(
                                            context,
                                            message: messageAssignedToMe,
                                          ))
                                      : ScrollPaginationContainer(
                                        onRefresh: () async {
                                          await assignedToMeNotifier.refresh();
                                        },
                                        onLoadMore: () async {
                                          await assignedToMeNotifier
                                              .loadMoreRecords();
                                        },
                                        isLoading:
                                            paginationStateTask.isLoading,
                                        hasMore: paginationStateTask.hasMore,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                        ),
                                        children: [
                                          AssignedToMeListItem(
                                            context,
                                            documentList:
                                                paginationStateTask.records,
                                            status: status,
                                            showDownloadButton: true,
                                            selectedStatus: _selectedStatus,
                                            selectedTabIndex: 0,
                                            iconClicked: 'Letter request',
                                            isVisible: false,
                                            hideContainerForScreens: true,
                                            removeTopBottomPadding: true,
                                          ),
                                        ],
                                      ),
                            ),
                          ],
                        );
                      },
                      isLoading: paginationStateTask.isLoading,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}

class StaticDocumentTypes {
  static const List<String> documentTypes = [
    "Request Bank Change",
    "Request Business Card",
    "Request Embassy Letter",
    "Request Medical Fitness Letter",
    "Request NOC for Drivers License",
    "Request Residency Letter",
    "Request Salary Certificate",
    "Request Salary Transfer",
  ];
}

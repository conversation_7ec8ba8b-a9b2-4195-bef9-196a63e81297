import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/utils/style/colors.dart';

class VerticalStatusIndicator extends StatelessWidget {
  final List<StatusItem> items;
  final String currentStatus;
  final Color lineColor;
  final Color activeColor;
  final Color inactiveColor;
  final Color backgroundColor;
  final double spacing;
  final double circleSize;
  final double innerCircleSize;
  final double lineWidth;
  final TextStyle? statusTextStyle;
  final TextStyle? timestampTextStyle;

  const VerticalStatusIndicator({
    Key? key,
    required this.items,
    required this.currentStatus,
    this.lineColor = AppColors.inputfillColor,
    this.activeColor = AppColors.green,
    this.inactiveColor = AppColors.lightGreyshade,
    this.backgroundColor = AppColors.inputfillColor,
    this.spacing = 40,
    this.circleSize = 30,
    this.innerCircleSize = 20,
    this.lineWidth = 9,
    this.statusTextStyle,
    this.timestampTextStyle,
  }) : super(key: key);

  bool isCompleted(String itemStatus) {
    final currentStatusLower = currentStatus.toLowerCase();
    final itemStatusLower = itemStatus.toLowerCase();

    if (currentStatusLower == 'new') {
      return itemStatusLower == 'new';
    } else if (currentStatusLower == 'in progress') {
      return ['new', 'in progress'].contains(itemStatusLower);
    } else if (currentStatusLower == 'closed') {
      return true; // All steps are completed when status is closed
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final styleStatus =
        statusTextStyle ??
        GoogleFonts.openSans(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: AppColors.blackColor,
        );

    final styleTimestamp =
        timestampTextStyle ??
        GoogleFonts.openSans(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: AppColors.lightBlack,
        );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Stack(
        children: [
          // Background vertical line
          Positioned(
            left: (circleSize - lineWidth) / 2,
            top: circleSize / 2,
            height:
                items.length > 1
                    ? (items.length - 1) * (spacing + circleSize)
                    : 0,
            child: Container(width: lineWidth, color: lineColor),
          ),
          // Status items column
          Column(
            children: List.generate(items.length, (index) {
              final item = items[index];
              final isStatusCompleted = isCompleted(item.status);
              final isLast = index == items.length - 1;

              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: circleSize,
                                height: circleSize,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: backgroundColor,
                                ),
                              ),
                              Container(
                                width: innerCircleSize,
                                height: innerCircleSize,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color:
                                      isStatusCompleted
                                          ? activeColor
                                          : inactiveColor,
                                ),
                                child:
                                    isStatusCompleted
                                        ? const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 14,
                                        )
                                        : null,
                              ),
                            ],
                          ),
                          const SizedBox(width: 12),
                          Text(item.status, style: styleStatus),
                        ],
                      ),
                      if (item.timestamp.isNotEmpty)
                        Text(item.timestamp, style: styleTimestamp),
                    ],
                  ),
                  if (!isLast) SizedBox(height: spacing),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}

class StatusItem {
  final String status;
  final String timestamp;

  StatusItem({required this.status, required this.timestamp});
}

// this is how data is passed to the widget
//  final statusItems = [
//       StatusItem(
//         status: 'New',
//         timestamp: '10th July, 2024, 03:18 pm',
//       ),
//       StatusItem(
//         status: 'In progress',
//         timestamp: '10th July, 2024, 04:26 pm',
//       ),
//       StatusItem(
//         status: 'Resolved',
//         timestamp: '10th July, 2024, 04:26 pm',
//       ),
//     ];
